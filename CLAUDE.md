# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

```bash
# Development
npm run dev          # Start development server (auto-kills port 3000 conflicts)
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Port management (dev script handles this automatically)
kill -9 $(lsof -t -i:3000)  # Kill processes on port 3000 if needed
```

## Architecture Overview

This is a **Next.js 14 SaaS AI chatbot platform** for social media business owners, built with:

- **App Router** architecture with server/client component separation
- **Supabase** for authentication, database, and file storage with RLS
- **Upstash Redis** for caching and session management
- **TypeScript** with strict mode and path aliases (`@/*` → `./src/*`)
- **Tailwind CSS** with custom jade-purple theme
- **Framer Motion** for animations and transitions

### Key Directories

- `src/app/` - Next.js App Router pages and API routes
- `src/components/` - Reusable components (UI components in `/ui/` subdirectory)
- `src/context/` - React Context providers (AuthContext, LanguageContext, LoadingContext)
- `src/utils/` - Utility functions for Supabase, Redis, auth, and image processing

### API Structure

- `src/app/api/auth/` - Authentication endpoints
- `src/app/api/dashboard/` - Dashboard data APIs
- `src/app/api/knowledge/` - Knowledge base management
- `src/app/api/payment/` - Payment processing
- `src/app/api/platform/` - Social media platform integrations

## Development Patterns

### Authentication
```typescript
// Server-side auth verification
const { authenticated, clientId } = await verifyAuth()

// Client-side auth context
const { user, session, signIn, signOut } = useAuth()
```

### Data Management
- Supabase for persistent data with Row Level Security (RLS)
- Redis for caching and temporary data
- Custom hooks for optimized data fetching

### Internationalization
- Dual language support (Khmer/English) via LanguageContext
- Translation function: `const { t } = useLanguage()`

### Styling
- Mobile-first responsive design
- Custom color palette with jade-purple theme
- Framer Motion for page transitions and micro-interactions

## Important Configuration

- **TypeScript paths**: Use `@/` for imports from `src/`
- **Image optimization**: Configured for Supabase storage domains
- **Font loading**: Inter and Plus Jakarta Sans fonts
- **Environment**: Development uses webpack polling for file watching