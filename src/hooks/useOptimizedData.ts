'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
// Removed cache imports - using simple sessionStorage for dashboard cache only

export interface UseOptimizedDataOptions {
  enableCache?: boolean
  refetchInterval?: number
  staleTime?: number
}

export interface PhotoData {
  id: number
  photo_id: string
  photo_url: string[] | null
  photo_file_path: string[] | null
  updated_at?: string
}

export interface FaqData {
  id: string
  question: string
  answer: string
  audio_url?: string
  photo_url?: string
  updated_at: string
}

export interface DashboardData {
  clientInfo: {
    client_id: string
    username: string
    sector: string | null
    lang: string | null
    plan_type: string | null
    next_billing_date: string | null
  }
  usageData: {
    usage_used: number
    usage_limit: number
  }
  knowledgeStats: {
    faqCount: number
    photoCount: number
    faqLimit: number
    photoLimit: number
    faqUsagePercentage: number
    photoUsagePercentage: number
  }
}

// Global request deduplication and client-side caching for dashboard data
let dashboardRequestPromise: Promise<any> | null = null
let photosRequestPromise: Promise<any> | null = null

const CLIENT_CACHE_DURATION = 30 * 60 * 1000 // 30 minutes client-side cache
const CACHE_KEY = 'dashboard_cache'
const PHOTOS_CACHE_KEY = 'photos_cache'

// Simple encryption/decryption for session storage
const ENCRYPTION_KEY = 'chhlat_dashboard_2025' 

const simpleEncrypt = (text: string): string => {
  try {
    // Simple XOR encryption with base64 encoding for obfuscation
    const key = ENCRYPTION_KEY
    let encrypted = ''

    for (let i = 0; i < text.length; i++) {
      const keyChar = key.charCodeAt(i % key.length)
      const textChar = text.charCodeAt(i)
      encrypted += String.fromCharCode(textChar ^ keyChar)
    }

    // Base64 encode the result to make it unreadable
    return btoa(encrypted)
  } catch (error) {
    console.warn('Encryption failed, storing as plain text:', error)
    return text
  }
}

const simpleDecrypt = (encryptedText: string): string => {
  try {
    // Decode base64 first
    const decoded = atob(encryptedText)
    const key = ENCRYPTION_KEY
    let decrypted = ''

    for (let i = 0; i < decoded.length; i++) {
      const keyChar = key.charCodeAt(i % key.length)
      const encryptedChar = decoded.charCodeAt(i)
      decrypted += String.fromCharCode(encryptedChar ^ keyChar)
    }

    return decrypted
  } catch (error) {
    console.warn('Decryption failed:', error)
    return encryptedText
  }
}

// Session storage-based cache that persists across page refreshes with encryption
const getClientCache = (): { data: DashboardData; timestamp: number } | null => {
  if (typeof window === 'undefined') return null

  try {
    const cached = sessionStorage.getItem(CACHE_KEY)
    if (!cached) return null

    // Try to decrypt the cached data
    const decrypted = simpleDecrypt(cached)
    const parsed = JSON.parse(decrypted)

    // Validate the cache structure
    if (parsed && typeof parsed.timestamp === 'number' && parsed.data) {
      return parsed
    }
    return null
  } catch (error) {
    console.warn('Error reading/decrypting dashboard cache:', error)
    // Clear corrupted cache
    try {
      sessionStorage.removeItem(CACHE_KEY)
    } catch (e) {
      // Ignore cleanup errors
    }
    return null
  }
}

const setClientCache = (data: DashboardData): void => {
  if (typeof window === 'undefined') return

  try {
    const cacheData = { data, timestamp: Date.now() }
    const jsonString = JSON.stringify(cacheData)
    const encrypted = simpleEncrypt(jsonString)
    sessionStorage.setItem(CACHE_KEY, encrypted)
  } catch (error) {
    console.warn('Error saving/encrypting dashboard cache:', error)
    // Storage might be full or disabled, continue without caching
  }
}

// Update cache data without resetting timestamp (for FAQ/photo count updates)
const updateClientCacheData = (data: DashboardData): void => {
  if (typeof window === 'undefined') return

  try {
    const cached = getClientCache()
    if (!cached) return

    // Keep the original timestamp - don't reset cache expiry
    const cacheData = { data, timestamp: cached.timestamp }
    const jsonString = JSON.stringify(cacheData)
    const encrypted = simpleEncrypt(jsonString)
    sessionStorage.setItem(CACHE_KEY, encrypted)
  } catch (error) {
    console.warn('Error updating dashboard cache:', error)
  }
}

export const clearClientCache = (): void => {
  if (typeof window === 'undefined') return

  try {
    sessionStorage.removeItem(CACHE_KEY)
  } catch (error) {
    console.warn('Error clearing dashboard cache:', error)
  }
}

/**
 * Update FAQ count in the dashboard cache and trigger UI refresh
 * Call this after adding/deleting FAQs to keep cache in sync
 */
export const updateFaqCountInCache = (newFaqCount: number): void => {
  if (typeof window === 'undefined') return

  try {
    const cached = getClientCache()
    if (!cached) {
      return
    }

    // Calculate new usage percentage
    const faqLimit = cached.data.knowledgeStats.faqLimit
    const newUsagePercentage = faqLimit > 0 ? Math.min((newFaqCount / faqLimit) * 100, 100) : 0

    // Update the cache with new values (preserve original timestamp)
    const updatedData = {
      ...cached.data,
      knowledgeStats: {
        ...cached.data.knowledgeStats,
        faqCount: newFaqCount,
        faqUsagePercentage: Math.round(newUsagePercentage)
      }
    }

    updateClientCacheData(updatedData)

    // Trigger UI refresh by dispatching a custom event (only for active knowledge pages)
    window.dispatchEvent(new CustomEvent('dashboardCacheUpdated', {
      detail: {
        type: 'faq',
        newCount: newFaqCount,
        newUsagePercentage: Math.round(newUsagePercentage),
        targetPage: 'knowledge' // Only update knowledge-related components
      }
    }))
  } catch (error) {
    console.warn('Error updating FAQ count in cache:', error)
  }
}

/**
 * Update photo count in the dashboard cache
 * Call this after adding/deleting photos to keep cache in sync
 */
export const updatePhotoCountInCache = (newPhotoCount: number): void => {
  if (typeof window === 'undefined') return

  try {
    const cached = getClientCache()
    if (!cached) {
      return
    }

    // Calculate new usage percentage
    const photoLimit = cached.data.knowledgeStats.photoLimit
    const newUsagePercentage = photoLimit > 0 ? Math.min((newPhotoCount / photoLimit) * 100, 100) : 0

    // Update the cache with new values (preserve original timestamp)
    const updatedData = {
      ...cached.data,
      knowledgeStats: {
        ...cached.data.knowledgeStats,
        photoCount: newPhotoCount,
        photoUsagePercentage: Math.round(newUsagePercentage)
      }
    }

    updateClientCacheData(updatedData)

    // Trigger UI refresh by dispatching a custom event (only for active knowledge pages)
    window.dispatchEvent(new CustomEvent('dashboardCacheUpdated', {
      detail: {
        type: 'photo',
        newCount: newPhotoCount,
        newUsagePercentage: Math.round(newUsagePercentage),
        targetPage: 'knowledge' // Only update knowledge-related components
      }
    }))
  } catch (error) {
    console.warn('Error updating photo count in cache:', error)
  }
}

/**
 * Hook for optimized dashboard data fetching with client-side caching and request deduplication
 */
export function useDashboardData(options: UseOptimizedDataOptions = {}) {
  const [data, setData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Check client-side cache first
      const clientCache = getClientCache()
      if (clientCache && Date.now() - clientCache.timestamp < CLIENT_CACHE_DURATION) {
        // Use cached data - no API call needed!
        setData(clientCache.data)
        setLoading(false)
        return
      }

      // If there's already a request in progress, wait for it instead of making a new one
      if (dashboardRequestPromise) {
        const result = await dashboardRequestPromise
        if (result.success) {
          setData(result.data)
          setClientCache(result.data) // Cache the result
        } else {
          throw new Error(result.error || 'Unknown error')
        }
        return
      }

      // Create new request
      dashboardRequestPromise = fetch('/api/dashboard/data', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then(response => {
        if (!response.ok) {
          throw new Error('Failed to fetch dashboard data')
        }
        return response.json()
      })

      const result = await dashboardRequestPromise

      if (result.success) {
        setData(result.data)
        setClientCache(result.data) // Cache the result for future navigations
      } else {
        throw new Error(result.error || 'Unknown error')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
      // Clear the request promise after completion (success or failure)
      dashboardRequestPromise = null
    }
  }, [])

  // Initialize with cached data immediately on mount (before any API calls)
  useEffect(() => {
    const clientCache = getClientCache()
    if (clientCache) {
      const age = Date.now() - clientCache.timestamp
      const isValid = age < CLIENT_CACHE_DURATION
    }

    if (clientCache && Date.now() - clientCache.timestamp < CLIENT_CACHE_DURATION) {
      setData(clientCache.data)
      setLoading(false)
      return // Don't fetch if we have valid cache
    }
    // Only fetch if no valid cache
    fetchData()
  }, []) // Empty dependency array - run only once on mount

  // Listen for cache updates and refresh UI immediately (only when necessary)
  useEffect(() => {
    const handleCacheUpdate = (event: CustomEvent) => {
      const detail = event.detail
      const currentPath = window.location.pathname

      // Only update if:
      // 1. We have data (component is active)
      // 2. We're on a knowledge page OR it's a general update
      const shouldUpdate = data && (
        currentPath.includes('/knowledge') ||
        !detail?.targetPage ||
        detail.targetPage === 'all'
      )

      if (shouldUpdate) {
        // Get the updated cache data and set it immediately
        const cached = getClientCache()
        if (cached) {
          setData(cached.data)
        }
      }
    }

    // Add event listener for cache updates
    window.addEventListener('dashboardCacheUpdated', handleCacheUpdate as EventListener)

    // Cleanup
    return () => {
      window.removeEventListener('dashboardCacheUpdated', handleCacheUpdate as EventListener)
    }
  }, [data]) // Depend on data to avoid unnecessary updates

  return { data, loading, error, refetch: fetchData }
}

/**
 * Hook for optimized knowledge page stats (FAQ count, photo count, limits)
 */
export function useKnowledgeStats(options: UseOptimizedDataOptions = {}) {
  const [data, setData] = useState<{
    counts: { faqs: number; photos: number }
    limits: { faqs: number; photos: number; plan_name: string }
    usage: { faq_percentage: number; photo_percentage: number }
  } | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/knowledge/stats', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch knowledge stats')
      }

      const result = await response.json()

      if (result.success) {
        setData(result.data)
      } else {
        throw new Error(result.error || 'Unknown error')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return { data, loading, error, refetch: fetchData }
}

// Removed useFaqData - replaced by direct Supabase calls in knowledge pages

// Removed usePhotoData - replaced by direct Supabase calls in knowledge pages

// Removed useFaqMutations - replaced by direct Supabase calls in knowledge pages

// Removed usePhotoMutations - replaced by direct Supabase calls in knowledge pages

/**
 * Update photo data in the cache
 */
export const updatePhotosInCache = (photos: PhotoData[]): void => {
  if (typeof window === 'undefined') return

  try {
    const cacheData = { data: photos, timestamp: Date.now() }
    const jsonString = JSON.stringify(cacheData)
    const encrypted = simpleEncrypt(jsonString)
    sessionStorage.setItem(PHOTOS_CACHE_KEY, encrypted)

    // Dispatch event to notify listeners of cache update
    window.dispatchEvent(new CustomEvent('photosDataUpdate', {
      detail: { type: 'photos' }
    }))
  } catch (error) {
    console.warn('Error updating photos cache:', error)
  }
}

/**
 * Get photos from cache
 */
export const getPhotosFromCache = (): { data: PhotoData[]; timestamp: number } | null => {
  if (typeof window === 'undefined') return null

  try {
    const cached = sessionStorage.getItem(PHOTOS_CACHE_KEY)
    if (!cached) return null

    const decrypted = simpleDecrypt(cached)
    const parsed = JSON.parse(decrypted)

    if (parsed && typeof parsed.timestamp === 'number' && Array.isArray(parsed.data)) {
      return parsed
    }
    return null
  } catch (error) {
    console.warn('Error reading photos cache:', error)
    try {
      sessionStorage.removeItem(PHOTOS_CACHE_KEY)
    } catch (e) {
      // Ignore cleanup errors
    }
    return null
  }
}

/**
 * Clear photos cache
 */
export const clearPhotosCache = (): void => {
  if (typeof window === 'undefined') return

  try {
    sessionStorage.removeItem(PHOTOS_CACHE_KEY)
  } catch (error) {
    console.warn('Error clearing photos cache:', error)
  }
}

/**
 * Hook for optimized photo data with caching and request deduplication
 */
export function usePhotosData(options: UseOptimizedDataOptions = {}) {
  const {
    enableCache = true,
    refetchInterval = 0,
    staleTime = CLIENT_CACHE_DURATION
  } = options

  const [data, setData] = useState<PhotoData[] | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const mountedRef = useRef(true)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const fetchPhotos = useCallback(async (force = false) => {
    try {
      // Check cache first if enabled and not forced refresh
      if (enableCache && !force) {
        const cached = getPhotosFromCache()
        if (cached && Date.now() - cached.timestamp < staleTime) {
          if (mountedRef.current) {
            setData(cached.data)
            setLoading(false)
          }
          return
        }
      }

      // Use existing promise if one is in flight
      if (!force && photosRequestPromise) {
        const data = await photosRequestPromise
        if (mountedRef.current) {
          setData(data)
          setLoading(false)
        }
        return
      }

      // Create new request promise
      photosRequestPromise = fetch('/api/knowledge/photos')
        .then(res => {
          if (!res.ok) throw new Error('Failed to fetch photos')
          return res.json()
        })
        .then(data => {
          if (data.error) throw new Error(data.error)
          const photos = data.photos || []
          if (enableCache) {
            updatePhotosInCache(photos)
          }
          return photos
        })
        .finally(() => {
          photosRequestPromise = null
        })

      const photos = await photosRequestPromise
      if (mountedRef.current) {
        setData(photos)
        setError(null)
      }
    } catch (err) {
      console.error('Error fetching photos:', err)
      if (mountedRef.current) {
        setError(err as Error)
      }
    }
    if (mountedRef.current) {
      setLoading(false)
    }
  }, [enableCache, staleTime])

  // Initialize with cached data immediately on mount
  useEffect(() => {
    mountedRef.current = true;
    
    if (enableCache) {
      const cached = getPhotosFromCache()
      if (cached && Date.now() - cached.timestamp < staleTime) {
        setData(cached.data)
        setLoading(false)
        return
      }
    }
    fetchPhotos()

    return () => {
      mountedRef.current = false;
    }
  }, [enableCache, staleTime, fetchPhotos])

  // Set up refetch interval if specified
  useEffect(() => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }

    // Only set up new interval if refetchInterval is positive
    if (refetchInterval > 0) {
      intervalRef.current = setInterval(() => {
        if (mountedRef.current) {
          fetchPhotos(true) // Force refresh on interval
        }
      }, refetchInterval)
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [refetchInterval, fetchPhotos])

  // Listen for cache update events
  useEffect(() => {
    const handleCacheUpdate = (event: CustomEvent) => {
      if (event.detail?.type === 'photos' && mountedRef.current) {
        // Just get data from cache instead of fetching
        const cached = getPhotosFromCache();
        if (cached) {
          setData(cached.data);
        }
      }
    }

    window.addEventListener('photosDataUpdate' as any, handleCacheUpdate)
    return () => {
      window.removeEventListener('photosDataUpdate' as any, handleCacheUpdate)
      mountedRef.current = false
    }
  }, []) // Remove fetchPhotos from dependencies since we're not using it anymore

  return {
    data,
    loading,
    error,
    refetch: () => fetchPhotos(true)
  }
}

export const clearAllCaches = (): void => {
  if (typeof window === 'undefined') return

  try {
    sessionStorage.removeItem(CACHE_KEY)
    sessionStorage.removeItem(PHOTOS_CACHE_KEY)
  } catch (error) {
    console.warn('Error clearing caches:', error)
  }
}
