import { Raleway, Nunito, Caladea, Zilla_Slab, <PERSON><PERSON><PERSON>, Noto_Sans_Khmer } from 'next/font/google'

// Define our primary fonts
export const raleway = Raleway({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-raleway',
  weight: ['300', '400', '500', '600', '700', '800'],
})

export const nunito = Nunito({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-nunito',
  weight: ['300', '400', '500', '600', '700', '800'],
})

// Define our secondary fonts (keep as backup)
export const caladea = Caladea({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-caladea',
  weight: ['400', '700'],
})

export const zillaSlab = Zilla_Slab({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-zilla-slab',
  weight: ['300', '400', '500', '600', '700'],
})

// Add Khmer fonts
export const dangrek = <PERSON><PERSON>rek({
  weight: '400',
  subsets: ['khmer'],
  display: 'swap',
  variable: '--font-khmer-title',
})

export const notoSansKhmer = Noto_Sans_Khmer({
  subsets: ['khmer'],
  display: 'swap',
  variable: '--font-khmer-body',
})

// Export all font variables as a combined string for easy use in className
export const fontVariables = `${raleway.variable} ${nunito.variable} ${caladea.variable} ${zillaSlab.variable} ${dangrek.variable} ${notoSansKhmer.variable}`
