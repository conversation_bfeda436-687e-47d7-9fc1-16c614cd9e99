'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// Define the theme type
export type Theme = 'dark' | 'light'

// Define the context type
interface ThemeContextType {
  theme: Theme
  setTheme: (theme: Theme) => void
  toggleTheme: () => void
}

// Create the context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

// Define theme configuration
export const themes = {
  dark: {
    // Main backgrounds
    background: 'bg-deep-blue',
    pageBackground: 'min-h-screen bg-deep-blue flex flex-col relative',
    
    // New HSL color variables (hsl_ prefix to avoid conflicts)
    hsl_bgDark: 'bg-[hsl(267_100%_5%)]',
    hsl_bg: 'bg-[hsl(272_100%_8%)]',
    hsl_bgLight: 'bg-[hsl(273_89%_12%)]',
    hsl_text: 'text-[hsl(266_100%_100%)]',
    hsl_textMuted: 'text-[hsl(266_57%_77%)]',
    hsl_highlight: 'bg-[hsl(271_52%_48%)]',
    hsl_border: 'border-[hsl(274_75%_34%)]',
    hsl_borderMuted: 'border-[hsl(279_100%_19%)]',
    hsl_primary: 'bg-[hsl(268_100%_81%)]',
    hsl_secondary: 'bg-[hsl(73_100%_37%)]',
    hsl_danger: 'bg-[hsl(7_99%_66%)]',
    hsl_warning: 'bg-[hsl(53_100%_22%)]',
    hsl_success: 'bg-[hsl(163_100%_24%)]',
    hsl_info: 'bg-[hsl(217_100%_70%)]',
    
    // Background effects
    backgroundEffects: (
      <>
        <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
        <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>
      </>
    ),
    
    // Header styles
    headerBackground: 'bg-white/[0.075]',
    headerBorder: 'border-white/20',
    headerHoverBorder: 'hover:border-white/30',
    
    // Card styles
    cardBackground: 'bg-white/[0.075]',
    cardBorder: 'border-white/20',
    cardHoverBorder: 'hover:border-white/40',

    // Secondary Card styles
    secondCardBackground: 'bg-transparent',

    // Tab Card Styles
    connectTabCardBackground: 'bg-white/10',
    connectTabCardBorder: 'border-white/30',
    
    // Text colors
    textPrimary: 'text-white',
    textSecondary: 'text-zinc-300',
    textMuted: 'text-zinc-400',
    
    // Logo
    logo: '/images/white_tran_logo.svg',
    
    // Settings button
    settingsBackground: 'bg-white/[0.75]',
    settingsBorder: 'border-white/20',
    settingsText: 'text-zinc-300',
    settingsHover: 'hover:text-white hover:bg-white/10 hover:border-white/20',
    
    // Language switcher
    languageSwitcherBackground: 'bg-white/10',
    languageSwitcherBorder: 'border-white/30',
    languageSwitcherText: 'text-zinc-300',
    languageSwitcherHover: 'hover:text-white hover:bg-jade-purple/20 hover:border-jade-purple/40',
    
    // Input styles
    inputBackground: 'bg-black/30',
    inputBorder: 'border-white/30',
    inputText: 'text-white',
    inputBorderFocus: 'focus:border-white/40',
    
    // Progress bar
    progressBackground: 'bg-white/10',
    
    // Circular stats
    statCircleBackground: 'rgba(0, 0, 0, 0.3)',
    statCircleBorder: 'rgba(255, 255, 255, 0.1)', 
    statCircleTrack: 'rgba(255, 255, 255, 0.15)',
    
    // Loading skeleton
    skeletonCard: 'bg-black/30 border-white/20',
    skeletonElement: 'bg-zinc-700/30',
    
    // Error/Warning styles
    errorBackground: 'bg-red-500/10',
    errorBorder: 'border-red-500/30',
    errorText: 'text-red-200',
    errorShadow: 'shadow-lg shadow-red-500/10',
    
    warningBackground: 'bg-amber-500/10',
    warningBorder: 'border-white/20',
    warningText: 'text-amber-200',
    warningShadow: 'shadow-lg shadow-white/10',
    
    usageWarningBackground: 'bg-orange-500/10',
    usageWarningBorder: 'border-orange-500/30',
    usageWarningText: 'text-orange-200',
    usageWarningShadow: 'shadow-lg shadow-orange-500/10',
    
    // Badge/tag styles
    badgeBackground: 'bg-white/10',
    badgeText: 'text-zinc-300',
    badgeBorder: 'border-white/20',
    
    // Footer styles
    footerBackground: 'bg-white/[0.05]',
    footerBorder: 'border-white/20',
    footerHoverBorder: 'hover:border-white/30',
    footerText: 'text-white',
    footerTextSecondary: 'text-zinc-300',
    footerTextMuted: 'text-zinc-400',
    footerLogo: '/images/white_tran_logo.svg',
    footerSocialBackground: 'bg-white/10',
    footerSocialBorder: 'border-white/20',
    footerSocialText: 'text-zinc-300',
    footerSocialHover: 'hover:text-white hover:bg-jade-purple/20 hover:border-jade-purple/40',
    footerDivider: 'border-white/10',
    
    // Footer component
    footerComponent: 'Footer'
  },
  light: {
    // Main backgrounds
    background: 'bg-[#faf9f6]',
    pageBackground: 'min-h-screen bg-[#faf9f6] flex flex-col relative',
    
    // New HSL color variables for light theme (hsl_ prefix to avoid conflicts)
    hsl_bgDark: 'bg-[hsl(249_25%_91%)]',
    hsl_bg: 'bg-[hsl(249_60%_96%)]',
    hsl_bgLight: 'bg-[hsl(249_100%_100%)]',
    hsl_text: 'text-[hsl(254_45%_6%)]',
    hsl_textMuted: 'text-[hsl(250_11%_31%)]',
    hsl_highlight: 'bg-[hsl(249_100%_100%)]',
    hsl_border: 'border-[hsl(250_8%_53%)]',
    hsl_borderMuted: 'border-[hsl(250_12%_65%)]',
    hsl_primary: 'bg-[hsl(255_34%_35%)]',
    hsl_secondary: 'bg-[hsl(62_100%_12%)]',
    hsl_danger: 'bg-[hsl(9_21%_41%)]',
    hsl_warning: 'bg-[hsl(52_23%_34%)]',
    hsl_success: 'bg-[hsl(147_19%_36%)]',
    hsl_info: 'bg-[hsl(217_22%_41%)]',
    
    // Background effects (none for light theme)
    backgroundEffects: null,
    
    // Header styles
    headerBackground: 'bg-white',
    headerBorder: 'border-gray-300',
    headerHoverBorder: 'hover:border-gray-400',
    
    // Card styles
    cardBackground: 'bg-white',
    cardBorder: 'border-gray-300',
    cardHoverBorder: 'hover:border-gray-400',

    // Secondary Card styles
    secondCardBackground: 'bg-white',

    // Tab Card Styles
    connectTabCardBackground: 'bg-gray-200',
    connectTabCardBorder: 'border-gray-300',
    
    // Text colors (inherit from body by default, override only when needed)
    textPrimary: 'text-zinc-900', // Explicit zinc-900 for primary text
    textSecondary: 'text-zinc-700', // Slightly lighter for secondary text
    textMuted: 'text-gray-600',
    
    // Input styles
    inputBackground: 'bg-gray-50',
    inputBorder: 'border-gray-300',
    inputText: 'text-zinc-900',
    inputBorderFocus: 'focus:border-gray-300',
    
    // Logo
    logo: '/images/purple_tran_logo.svg',
    
    // Settings button
    settingsBackground: 'bg-white',
    settingsBorder: 'border-gray-300',
    settingsText: 'text-zinc-900',
    settingsHover: 'hover:bg-gray-50 hover:border-gray-300',
    
    // Language switcher
    languageSwitcherBackground: 'bg-gray-200/50',
    languageSwitcherBorder: 'border-gray-300',
    languageSwitcherText: 'text-gray-600',
    languageSwitcherHover: 'hover:text-jade-purple-dark hover:bg-jade-purple/10 hover:border-jade-purple/40',
    
    // Progress bar
    progressBackground: 'bg-gray-200',
    
    // Circular stats
    statCircleBackground: '#faf9f6', // Use page background color
    statCircleBorder: 'rgba(0, 0, 0, 0.1)',
    statCircleTrack: 'rgba(0, 0, 0, 0.08)', // Lighter color for better light mode appearance
    
    // Loading skeleton  
    skeletonCard: 'bg-white border-gray-200',
    skeletonElement: 'bg-[#faf9f6]', // Use page background color for better integration
    
    // Error/Warning styles
    errorBackground: 'bg-white',
    errorBorder: 'border-red-200',
    errorText: 'text-red-700',
    errorShadow: '',
    
    warningBackground: 'bg-white',
    warningBorder: 'border-amber-200',
    warningText: 'text-amber-700',
    warningShadow: '',
    
    usageWarningBackground: 'bg-white',
    usageWarningBorder: 'border-orange-200',
    usageWarningText: 'text-orange-700',
    usageWarningShadow: '',
    
    // Badge/tag styles
    badgeBackground: 'bg-gray-200/50',
    badgeText: 'text-gray-700',
    badgeBorder: 'border-gray-300',
    
    // Footer styles
    footerBackground: 'bg-white',
    footerBorder: 'border-gray-300',
    footerHoverBorder: 'hover:border-gray-400',
    footerText: 'text-zinc-900', // Explicit zinc-900 for headers
    footerTextSecondary: 'text-zinc-700',
    footerTextMuted: 'text-gray-600',
    footerLogo: '/images/purple_tran_logo.svg',
    footerSocialBackground: 'bg-gray-50',
    footerSocialBorder: 'border-gray-200',
    footerSocialText: 'text-gray-600',
    footerSocialHover: 'hover:text-jade-purple hover:border-jade-purple',
    footerDivider: 'border-gray-200',
    
    // Footer component
    footerComponent: 'Footer'
  }
}

// Create the provider component
export function ThemeProvider({ children }: { children: ReactNode }) {
  // Default to dark theme
  const [theme, setThemeState] = useState<Theme>('dark')

  // Load theme preference from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        // Try to get theme from localStorage first
        let savedTheme = localStorage.getItem('uiTheme') as Theme

        // If not in localStorage, try to get from cookie as fallback
        if (!savedTheme || (savedTheme !== 'dark' && savedTheme !== 'light')) {
          const cookies = document.cookie.split(';')
          const themeCookie = cookies.find(cookie => cookie.trim().startsWith('uiTheme='))
          if (themeCookie) {
            savedTheme = themeCookie.split('=')[1].trim() as Theme
          }
        }

        // Set theme state if valid
        if (savedTheme && (savedTheme === 'dark' || savedTheme === 'light')) {
          setThemeState(savedTheme)
          // Apply theme class to dashboard containers
          const dashboardContainers = document.querySelectorAll('.dashboard-container')
          dashboardContainers.forEach(container => {
            container.className = container.className.replace(/\b(light-theme|dark-theme)\b/g, '')
            container.classList.add(`${savedTheme}-theme`)
          })
        } else {
          // Default to dark theme for dashboard containers
          const dashboardContainers = document.querySelectorAll('.dashboard-container')
          dashboardContainers.forEach(container => {
            container.classList.add('dark-theme')
          })
        }
      } catch (error) {
        console.error('Error loading theme preference:', error)
      }
    }
  }, [])

  // Handle theme change
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme)
    if (typeof window !== 'undefined') {
      try {
        // Set the theme in localStorage
        localStorage.setItem('uiTheme', newTheme)

        // Also set a cookie as a fallback for browsers with localStorage issues
        document.cookie = `uiTheme=${newTheme}; path=/; max-age=31536000; SameSite=Lax` // 1 year expiry

        // Apply theme class to dashboard containers
        const dashboardContainers = document.querySelectorAll('.dashboard-container')
        dashboardContainers.forEach(container => {
          container.className = container.className.replace(/\b(light-theme|dark-theme)\b/g, '')
          container.classList.add(`${newTheme}-theme`)
        })

        // console.log('Theme set in context:', newTheme)
      } catch (error) {
        console.error('Error setting theme:', error)
      }
    }
  }

  // Toggle between themes
  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark')
  }

  const value = {
    theme,
    setTheme,
    toggleTheme
  }

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
}

// Hook to use the theme context
export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

// Helper hook to get current theme configuration
export function useThemeConfig() {
  const { theme } = useTheme()
  return themes[theme]
}
