'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { FaFacebook, FaInstagram, FaTiktok, FaTelegram, FaYoutube } from 'react-icons/fa'
import { useLanguage } from '@/context/LanguageContext'
import { useThemeConfig } from '@/context/ThemeContext'

export default function Footer() {
  const currentYear = new Date().getFullYear()
  const { t } = useLanguage()
  const themeConfig = useThemeConfig()

  const footerLinks = [
    {
      titleKey: 'footer_product',
      links: [
        { nameKey: 'footer_features', href: '#features' },
        { nameKey: 'footer_pricing', href: '#pricing' },
        { nameKey: 'footer_how_it_works', href: '#how-it-works' },
        { nameKey: 'footer_faq', href: '#faq' },
      ]
    },
    {
      titleKey: 'footer_company',
      links: [
        { nameKey: 'footer_about', href: '#' },
        { nameKey: 'footer_blog', href: '#' },
        { nameKey: 'footer_careers', href: '#' },
        { nameKey: 'footer_contact', href: '#' },
      ]
    },
  ]

  const socialLinks = [
    { name: 'Facebook', icon: <FaFacebook className="text-lg" />, href: '#' },
    { name: 'TikTok', icon: <FaTiktok className="text-lg" />, href: '#' },
    { name: 'Instagram', icon: <FaInstagram className="text-lg" />, href: '#' },
    { name: 'YouTube', icon: <FaYoutube className="text-lg" />, href: '#' },
    { name: 'Telegram', icon: <FaTelegram className="text-lg" />, href: '#' },
  ]

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <footer className="text-white relative overflow-hidden mt-16">
      {/* Original footer design (commented out)
      <div className="bg-deep-blue text-white border-t border-zinc-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="grid grid-cols-12 gap-4 h-full">
            {Array.from({ length: 24 }).map((_, i) => (
              <div key={i} className="h-full w-px bg-white opacity-10" style={{ left: `${(i / 24) * 100}%` }}></div>
            ))}
            {Array.from({ length: 12 }).map((_, i) => (
              <div key={i} className="h-px w-full bg-white opacity-10" style={{ top: `${(i / 12) * 100}%` }}></div>
            ))}
          </div>
        </div>

        <div className="section py-12 relative z-10">
          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            <div className="col-span-2">
              <div
                className="mb-4 cursor-pointer"
                onClick={scrollToTop}
              >
                <Image
                  src="/images/white_tran_logo.svg"
                  alt="Chhlat Bot"
                  width={120}
                  height={40}
                  className="h-10 w-auto"
                />
              </div>
              <p className="font-body mb-6 text-gray-400 text-sm sm:text-base">
                Automate your social media customer service with AI ChatBot. Handle messages across Facebook, Telegram, Instagram, WhatsApp, and Website from one dashboard.
              </p>
              <div className="flex space-x-4">
                {socialLinks.map((social, index) => (
                  <motion.a
                    key={index}
                    href={social.href}
                    className="w-8 h-8 sm:w-10 sm:h-10 bg-white/15 rounded-full flex items-center justify-center text-white hover:bg-jade-purple/75 transition-all duration-300"
                    whileHover={{ y: -3 }}
                    aria-label={social.name}
                  >
                    {social.icon}
                  </motion.a>
                ))}
              </div>
            </div>

            {footerLinks.map((section, index) => (
              <div key={index}>
                <h4 className="text-lg font-bold mb-4 font-title">{section.title}</h4>
                <ul className="space-y-2">
                  {section.links.map((link, linkIndex) => (
                    <li key={linkIndex}>
                      <a
                        href={link.href}
                        className="text-gray-400 hover:text-white transition-colors duration-200 font-body text-sm"
                      >
                        {link.name}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          <div className="border-t border-zinc-800 mt-12 pt-6 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-500 text-sm font-body mb-4 md:mb-0">
              &copy; {currentYear} <span className="text-jade-purple">Chhlat Bot</span> All rights reserved.
            </p>
            <div className="flex flex-wrap justify-center gap-4 sm:gap-6 text-sm font-body">
              <a href="#" className="text-gray-500 hover:text-white transition-colors duration-200">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-500 hover:text-white transition-colors duration-200">
                Terms of Service
              </a>
              <a href="#" className="text-gray-500 hover:text-white transition-colors duration-200">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
      */}

      {/* Modern clean footer */}
      <div className="container mx-auto px-4 py-12 relative z-10">
        <div className={`relative ${themeConfig.footerBackground} backdrop-blur-xl rounded-2xl p-8 border ${themeConfig.footerBorder} ${themeConfig.footerHoverBorder} transition-all duration-300 overflow-hidden`}>
          {/* Simple background overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/3 to-transparent rounded-2xl"></div>

          {/* Content */}
          <div className="relative z-10">
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
              <div className="col-span-2">
                <div
                  className="mb-6 cursor-pointer group"
                  onClick={scrollToTop}
                >
                  <Image
                    src={themeConfig.footerLogo}
                    alt="Chhlat Bot"
                    width={120}
                    height={40}
                    className="h-10 w-auto transition-transform duration-300 group-hover:scale-105"
                  />
                </div>
                <p className={`font-body mb-8 ${themeConfig.footerTextSecondary} text-sm sm:text-base leading-relaxed`}>
                  {t('footer_description')}
                </p>
                <div className="flex space-x-3">
                  {socialLinks.map((social, index) => (
                    <motion.a
                      key={index}
                      href={social.href}
                      className={`w-10 h-10 sm:w-12 sm:h-12 ${themeConfig.footerSocialBackground} border ${themeConfig.footerSocialBorder} rounded-xl flex items-center justify-center ${themeConfig.footerSocialText} ${themeConfig.footerSocialHover} transition-all duration-50`}
                      whileHover={{ y: -1, scale: 1.05 }}
                      aria-label={social.name}
                    >
                      {social.icon}
                    </motion.a>
                  ))}
                </div>
              </div>

              {footerLinks.map((section, index) => (
                <div key={index}>
                  <h4 className={`text-lg font-semibold mb-6 font-title ${themeConfig.footerText}`}>{t(section.titleKey)}</h4>
                  <ul className="space-y-4">
                    {section.links.map((link, linkIndex) => (
                      <li key={linkIndex}>
                        <a
                          href={link.href}
                          className={`${themeConfig.footerTextMuted} hover:text-jade-purple transition-colors duration-300 font-body text-sm hover:translate-x-1 inline-block transform`}
                        >
                          {t(link.nameKey)}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>

            {/* Bottom section with improved styling */}
            <div className={`mt-12 pt-8 border-t ${themeConfig.footerDivider}`}>
              <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <p className={`${themeConfig.footerTextMuted} text-sm font-body`}>
                  &copy; {currentYear} <span className="text-jade-purple font-semibold">ChhlatBot</span> {t('footer_rights')}
                </p>
                <div className="flex flex-wrap justify-center gap-6 text-sm font-body">
                  <a
                    href="/privacy"
                    className={`${themeConfig.footerTextMuted} hover:text-jade-purple transition-colors duration-300 hover:underline underline-offset-4`}
                  >
                    {t('footer_privacy')}
                  </a>
                  <a
                    href="/terms"
                    className={`${themeConfig.footerTextMuted} hover:text-jade-purple transition-colors duration-300 hover:underline underline-offset-4`}
                  >
                    {t('footer_terms')}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}