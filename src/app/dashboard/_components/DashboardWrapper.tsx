'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createClientComponentClient } from '@/utils/supabase/client'
// Removed clientInfo imports - now using dashboard cache as single source of truth
import { clearClientCache } from '@/hooks/useOptimizedData'

export default function DashboardWrapper({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const [isSigningOut, setIsSigningOut] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientComponentClient()

  // Set isClient to true when component mounts (client-side only)
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Simple authentication check without data fetching
  // Data fetching is handled by individual pages using optimized hooks
  useEffect(() => {
    if (!isClient) return

    async function checkAuth() {
      setIsLoading(true)
      try {
        // Simple auth check - let RLS handle the rest
        const { data: authData, error: authError } = await supabase.auth.getUser()

        if (authError || !authData.user) {
          clearClientCache()
          router.push('/access')
          return
        }
        // Individual pages will handle their own data fetching via hooks
      } catch (error: any) {
        console.error('❌ [WRAPPER] Error in auth check:', error)
        setError(`An unexpected error occurred: ${error.message}`)
        clearClientCache()
        router.push('/access')
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [isClient, supabase, router])

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true)

      // Clear client cache before signing out
      clearClientCache()

      // Sign out of Supabase (locally)
      const { error } = await supabase.auth.signOut()
      if (error) {
        throw error
      }

      // Clear cookies server-side
      await fetch('/api/auth/signout', {
        method: 'POST',
        cache: 'no-store'
      })

      // Dashboard cache is already cleared above - no separate client info cache needed

      // Redirect to access page using router
      router.push('/access')

      // For a complete reset of client state, refresh the page after navigation
      // but only if we're in the browser
      if (isClient) {
        setTimeout(() => {
          router.refresh()
        }, 100)
      }
    } catch (error) {
      console.error('Error signing out:', error)
      // If we encounter an error, still redirect to access page
      router.push('/access')
    } finally {
      setIsSigningOut(false) // Ensure signing out state is reset
    }
  }

  if (isLoading && isClient) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <div className="w-8 h-8 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-black px-4">
        <div className="max-w-md w-full bg-zinc-900 p-6 rounded-lg border border-zinc-800">
          <h2 className="text-xl text-red-400 font-bold mb-4">Access Error</h2>
          <p className="text-zinc-300 mb-6">{error}</p>
          <div className="flex justify-between">
            <button
              onClick={handleSignOut}
              disabled={isSigningOut}
              className="px-4 py-2 bg-zinc-800 text-white rounded hover:bg-zinc-700 transition-colors disabled:opacity-50"
            >
              {isSigningOut ? 'Signing Out...' : 'Sign Out'}
            </button>
            <button
              onClick={() => router.refresh()}
              className="px-4 py-2 bg-violet-600 text-white rounded hover:bg-violet-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="dashboard-wrapper dashboard-container">
      {children}
    </div>
  )
}
