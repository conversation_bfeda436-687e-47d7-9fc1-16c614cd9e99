'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import Footer from '@/components/Footer'
import { createClientComponentClient } from '@/utils/supabase/client'

import { useDashboardData, updateFaqCountInCache, usePhotosData } from '@/hooks/useOptimizedData'
import { v4 as uuidv4 } from 'uuid'
// Webhook calls are now handled by the update API endpoint
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { optimizeGalleryImage } from '@/utils/imageOptimization'
import { FaTrash } from 'react-icons/fa';
import { Button, LinkButton } from '@/components/ui'

// Optimized PhotoThumbnail component for fast loading without Vercel Image
const PhotoThumbnail = ({
  photo,
  className = "w-10 h-10",
  onClick
}: {
  photo: { photo_url: string[] | null, photo_id: string },
  className?: string,
  onClick?: () => void
}) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  return (
    <div
      className={`${className} ${themeConfig.cardBackground} rounded overflow-hidden flex-shrink-0 relative border ${themeConfig.cardBorder} ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {photo.photo_url && photo.photo_url.length > 0 && !imageError ? (
        <>
          {/* Loading placeholder - no animation */}
          <div className={`absolute inset-0 transition-opacity duration-200 ${imageLoaded ? 'opacity-0' : 'opacity-100'} ${theme === 'light' ? 'bg-gray-200' : 'bg-zinc-600'}`} />

          {/* Actual image */}
          <img
            src={photo.photo_url[0]}
            alt={photo.photo_id}
            className={`w-full h-full object-cover transition-opacity duration-200 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            loading="lazy"
            decoding="async"
            onLoad={() => setImageLoaded(true)}
            onError={() => {
              setImageError(true)
              setImageLoaded(true)
            }}
          />
        </>
      ) : (
        <div className={`w-full h-full flex items-center justify-center ${theme === 'light' ? 'bg-gray-100' : 'bg-white/5'} ${themeConfig.textMuted} text-xs`}>
          <svg width="24" height="18" viewBox="0 0 24 18" fill="currentColor">
            <rect width="24" height="18" rx="2" fill="currentColor" opacity="0.3"/>
            <path d="M3 13L7 9L11 13L15 6L21 12V15H3V13Z" fill="currentColor" opacity="0.6"/>
            <circle cx="7" cy="6" r="2" fill="currentColor" opacity="0.6"/>
          </svg>
        </div>
      )}
    </div>
  )
}

// Types for our database records
type FAQ = {
  id: number
  faq_id: string // Unique identifier for the FAQ
  question: string
  answer: string
  question_p?: string
  answer_p?: string
  created_at: string
  client_id: string
  audio_url?: string
  audio_duration?: number
  audio_file_path?: string
  photo_url?: any // JSONB field from database
  photo_id?: string // Product name field
  localAudio?: {
    url: string;
    duration: number;
  };
}

// Define the type for product info similar to the knowledge page
interface ProductInfo {
  prod_id: string;
  name: string;
  photo_url: string | null; // Thumbnail for display
  full_photo_urls: string[] | null; // Full array for saving to DB
}

export default function KnowledgeBasePage() {
  const supabase = createClientComponentClient()
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Use dashboard cache for knowledge stats and client info
  const { data: dashboardData, loading: isDashboardLoading } = useDashboardData()
  const clientInfo = dashboardData?.clientInfo
  const knowledgeStats = dashboardData?.knowledgeStats

  // Use optimized photos data hook
  const { data: photosData, loading: photosLoading } = usePhotosData()

  // Get knowledge stats from dashboard cache
  const totalFaqs = knowledgeStats?.faqCount || 0
  const totalFaqsLimit = knowledgeStats?.faqLimit || 0
  const faqUsagePercentage = knowledgeStats?.faqUsagePercentage || 0
  const isLoadingCount = isDashboardLoading

  // Add state to store all photos for local search
  const [allPhotos, setAllPhotos] = useState<Array<{
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }>>([])

  // Update allPhotos state when data changes
  useEffect(() => {
    if (photosData) {
      setAllPhotos(photosData)
    }
  }, [photosData])

  // Viewing popup state
  const [viewingItem, setViewingItem] = useState<{field: 'question' | 'answer', value: string} | null>(null)
  const viewModalRef = useRef<HTMLDivElement>(null)

  // Data states
  const [questions, setQuestions] = useState<FAQ[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Unified audio playback state
  const [audioPlayback, setAudioPlayback] = useState<{
    id: number
    audio: HTMLAudioElement | AudioBufferSourceNode
    duration: number
    remainingTime: number
    type: 'existing' | 'new'
  } | null>(null)
  
  // Web Audio API context for stereo playback
  const audioContext = useRef<AudioContext | null>(null)

  // Enhanced browser & mobile detection
  const initializeAudio = useCallback(() => {
    // Detect mobile and browser for audio routing
    const isMobile = 
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
      ('ontouchstart' in window && window.innerWidth < 1024);
    const isSafari = 
      /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

    console.log(`Audio initialization - Mobile: ${isMobile}, Safari: ${isSafari}`);

    // Initialize AudioContext if not already created
    if (!audioContext.current) {
      try {
        audioContext.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      } catch (error) {
        console.error('Failed to initialize AudioContext:', error);
      }
    }

    return { isMobile, isSafari };
  }, [])

  // Common audio utility functions
  const stopAudioPlayback = useCallback(() => {
    if (audioPlayback?.audio) {
      if ('pause' in audioPlayback.audio) {
        // HTML5 Audio element
        audioPlayback.audio.pause();
      } else if ('stop' in audioPlayback.audio) {
        // Web Audio API BufferSource
        try {
          (audioPlayback.audio as any).stop();
        } catch (e) {
          console.log('Web Audio source already stopped');
        }
      }
    }
    setAudioPlayback(null);
    audioRef.current = null;
  }, [audioPlayback])

  const pauseAudioPlayback = useCallback(() => {
    if (audioPlayback?.audio) {
      if ('pause' in audioPlayback.audio) {
        // HTML5 Audio element
        audioPlayback.audio.pause();
        setAudioPlayback(null);
        audioRef.current = null;
      } else if ('stop' in audioPlayback.audio) {
        // Web Audio API BufferSource - can't pause, only stop
        try {
          (audioPlayback.audio as any).stop();
        } catch (e) {
          console.log('Web Audio source already stopped');
        }
        setAudioPlayback(null);
        audioRef.current = null;
      }
    }
  }, [audioPlayback])


  // Virtual scrolling state
  const [visibleStartIndex, setVisibleStartIndex] = useState(0)
  const [visibleEndIndex, setVisibleEndIndex] = useState(14) // Show 15 items initially
  const itemHeight = 120 // Fixed height for each FAQ item
  const visibleItemsCount = 15 // Number of visible items

  // Search state
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredQuestions, setFilteredQuestions] = useState<FAQ[]>([])

  // Recently added state
  const [recentQA, setRecentQA] = useState<(FAQ & {productInfo?: ProductInfo}) | null>(null)
  const [isUpdating, setIsUpdating] = useState(false)

  // Photo search state
  const [selectedProduct, setSelectedProduct] = useState<ProductInfo | null>(null)
  const [photoSearchQuery, setPhotoSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [isPhotoLoading, setIsPhotoLoading] = useState(false)
  const [photoSearchResults, setPhotoSearchResults] = useState<Array<{
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }>>([])
  const [showPhotoResults, setShowPhotoResults] = useState(false)

  // Edit functionality
  const [editingItem, setEditingItem] = useState<{id: number, field: 'question' | 'answer', value: string} | null>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const modalRef = useRef<HTMLDivElement>(null)

  // Track if textarea has been focused for mobile behavior
  const [hasFocusedInput, setHasFocusedInput] = useState(false)

  // State for update status
  const [updateStatus, setUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateMessage, setUpdateMessage] = useState('')
  const [updateProgress, setUpdateProgress] = useState(0)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const confirmModalRef = useRef<HTMLDivElement>(null)

  // Delete confirmation
  const [deleteConfirm, setDeleteConfirm] = useState<{id: number, isAudio: boolean, audio_file_path?: string} | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const deleteModalRef = useRef<HTMLDivElement>(null)

  // Add a reference for the update section
  const updateSectionRef = useRef<HTMLDivElement>(null)

  // Add state variables for audio deletion and recording
  const [confirmAudioDelete, setConfirmAudioDelete] = useState<number | null>(null)
  const [isRecordingAudio, setIsRecordingAudio] = useState<boolean>(false)
  const [recordingItem, setRecordingItem] = useState<number | null>(null)
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null)
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null)
  const [isRecording, setIsRecording] = useState(false)
  const [recordingDuration, setRecordingDuration] = useState(0)
  const [recordingTimer, setRecordingTimer] = useState<NodeJS.Timeout | null>(null)
  const [isSavingAudio, setIsSavingAudio] = useState(false)
  const [isProcessingAudio, setIsProcessingAudio] = useState(false); // New state for post-recording processing
  const [newlyRecordedAudio, setNewlyRecordedAudio] = useState<{ id: number; url: string; duration: number; blob: Blob } | null>(null); // Store details of the latest recording
  // Unified audio ref for both existing and new audio
  const audioRef = useRef<HTMLAudioElement | AudioBufferSourceNode | null>(null);
  const audioDeleteModalRef = useRef<HTMLDivElement>(null) // Re-add this ref
  const audioRecordModalRef = useRef<HTMLDivElement>(null) // Re-add this ref
  
  // Add audio permission state
  const [audioPermissionState, setAudioPermissionState] = useState<'unknown' | 'granted' | 'denied' | 'requesting'>('unknown')
  const [isCheckingPermission, setIsCheckingPermission] = useState(false)

  // Add state variables for local audio blob
  const [localAudioBlob, setLocalAudioBlob] = useState<{ id: number, blob: Blob, url: string, duration: number } | null>(null)

  // Add state for cancel confirmation
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false)
  const cancelConfirmModalRef = useRef<HTMLDivElement>(null)

  // Add state for image gallery
  const [imageGallery, setImageGallery] = useState<{urls: string[], currentIndex: number} | null>(null)
  const imageGalleryRef = useRef<HTMLDivElement>(null)

  // Add touch support for image gallery
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // Navigation functions for image gallery
  const showPreviousImage = useCallback(() => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;
    const newIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
    setImageGallery({ ...imageGallery, currentIndex: newIndex });
  }, [imageGallery]);

  const showNextImage = useCallback(() => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;
    const newIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
    setImageGallery({ ...imageGallery, currentIndex: newIndex });
  }, [imageGallery]);

  // Reference for simple dropdown
  const searchResultsRef = useRef<HTMLDivElement>(null);

  // Photos data is now handled by the usePhotosData hook

  // Audio cleanup utility function (defined after state variables)
  const cleanupAudio = useCallback(() => {
    // Stop any playing audio
    stopAudioPlayback();
    
    // Clean up newly recorded audio blob URLs
    if (newlyRecordedAudio) {
      URL.revokeObjectURL(newlyRecordedAudio.url);
      setNewlyRecordedAudio(null);
    }
    
    // Clean up local audio blob URLs
    if (localAudioBlob) {
      URL.revokeObjectURL(localAudioBlob.url);
      setLocalAudioBlob(null);
    }
  }, [stopAudioPlayback, newlyRecordedAudio, localAudioBlob])

  // Removed fetchFaqCount - now using dashboard cache

  // Fetch questions via API (Security Enhanced)
  const fetchQuestions = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Call knowledge lists API
      const response = await fetch('/api/knowledge/lists')
      const responseData = await response.json()

      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to fetch knowledge items')
      }

      const processedData = responseData.items || []

      setQuestions(processedData);
      setFilteredQuestions(processedData);

      // FAQ count is now managed by dashboard cache
    } catch (err: any) {
      console.error('Error fetching questions:', err);
      setError(err.message || 'Failed to load questions');
    } finally {
      setIsLoading(false);
    }
  }

  // Incremental update functions for better performance
  const updateQuestionInState = (updatedQuestion: any) => {
    // Move the updated question to the top of the list
    setQuestions(prev => {
      const filtered = prev.filter(q => q.id !== updatedQuestion.id);
      return [updatedQuestion, ...filtered];
    });
    
    // Also update filtered questions while maintaining the current filter
    setFilteredQuestions(prev => {
      const filtered = prev.filter(q => q.id !== updatedQuestion.id);
      return [updatedQuestion, ...filtered];
    });
  }

  const removeQuestionFromState = (questionId: number) => {
    setQuestions(prev => prev.filter(q => q.id !== questionId))
    setFilteredQuestions(prev => prev.filter(q => q.id !== questionId))
  }

  // Client-side search function (no server search needed since we load all data)
  const performClientSearch = useCallback((query: string) => {
    if (!query.trim()) {
      setFilteredQuestions(questions);
      return;
    }

    const queryLower = query.toLowerCase();
    const filtered = questions.filter(q =>
      q.question.toLowerCase().includes(queryLower) ||
      q.answer.toLowerCase().includes(queryLower)
    );
    setFilteredQuestions(filtered);
  }, [questions]);

  // Load questions only after client info is available - but only on initial load
  useEffect(() => {
    if (clientInfo && questions.length === 0) {
      fetchQuestions()
    }
  }, [clientInfo]) // Remove questions from dependency to avoid re-fetch loops

  // Effect to filter questions based on search query (client-side only)
  useEffect(() => {
    if (!questions.length) return;
    performClientSearch(searchQuery);
  }, [searchQuery, questions, performClientSearch])

  // Calculate visible items for virtual scrolling (now handled by global scroll)



  const handleViewItem = (field: 'question' | 'answer', value: string) => {
    setViewingItem({ field, value })
  }

  // Move item to Recently Added section for editing
  const handleMoveToRecentlyAdded = (qa: FAQ) => {
    // Clear existing items and add just this one
    // If audio_url exists, set answer to empty string (audio and text answers cannot coexist)
    setRecentQA({
      id: qa.id,
      faq_id: qa.faq_id, // Include the faq_id
      question: qa.question,
      answer: qa.audio_url ? "" : qa.answer, // Set answer to empty string if audio exists
      audio_url: qa.audio_url,
      audio_duration: qa.audio_duration,
      audio_file_path: qa.audio_file_path,
      photo_url: qa.photo_url,
      photo_id: qa.photo_id,
      // These are not used in the recentQA context, but we include them to satisfy the type system
      created_at: qa.created_at,
      client_id: qa.client_id
    })

    // If the item has an image, set it as the selected product
    if (qa.photo_url) {
      const imageData = extractImageUrls(qa.photo_url);
      if (imageData.firstUrl) {
        setSelectedProduct({
          prod_id: `img_${qa.id}`,
          name: qa.photo_id || "Attached Image",
          photo_url: imageData.firstUrl,
          full_photo_urls: imageData.allUrls
        });
      }
    } else {
      // Reset selected product if no image
      setSelectedProduct(null);
    }

    // Reset photo search input and results
    setPhotoSearchQuery('');
    setPhotoSearchResults([]);
    setShowPhotoResults(false);

    // Scroll to update section
    setTimeout(() => {
      if (updateSectionRef.current) {
        updateSectionRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
      }
    }, 100)
  }



  // Check if there are any changes to the item being edited
  const hasChanges = () => {
    if (!recentQA) return false;

    // Find the original question from the questions array
    const originalQuestion = questions.find(q => q.id === recentQA.id);

    if (!originalQuestion) return false;

    // Special case: If the original has audio and recentQA has empty answer but no changes to audio,
    // this is not a real change (it's just the initial state when editing an audio answer)
    const isInitialAudioState =
      originalQuestion.audio_url &&
      recentQA.audio_url === originalQuestion.audio_url &&
      recentQA.answer === "" &&
      !newlyRecordedAudio &&
      !recentQA.localAudio;

    // Check for text changes, but ignore the special case for audio answers
    const hasTextChanges = !isInitialAudioState && recentQA.answer !== originalQuestion.answer;

    const hasAudioChanges =
      // New recording added
      newlyRecordedAudio !== null ||
      // Currently recording
      isRecording ||
      // Audio deleted (was present in original but not in current)
      (originalQuestion.audio_url && !recentQA.audio_url) ||
      // Local audio added
      recentQA.localAudio !== undefined;

    // Check for photo changes
    const hasPhotoChanges =
      // Photo added or changed
      (selectedProduct !== null &&
        (
          // No photo originally but now has one
          (!originalQuestion.photo_url && selectedProduct) ||
          // Photo changed (different name)
          (originalQuestion.photo_id !== selectedProduct.name)
        )
      ) ||
      // Photo removed
      (originalQuestion.photo_url && !selectedProduct);

    // Determine if any changes have been made
    return hasTextChanges || hasAudioChanges || hasPhotoChanges;
  }

  // Check if the item has a valid answer (either text or audio)
  const hasValidAnswer = () => {
    if (!recentQA) return false;

    // Check if there's a text answer
    const hasTextAnswer = recentQA.answer && recentQA.answer.trim() !== '';

    // Check if there's an audio answer
    const hasAudioAnswer =
      // Existing audio URL
      recentQA.audio_url !== undefined && recentQA.audio_url !== null ||
      // Newly recorded audio
      newlyRecordedAudio !== null ||
      // Local audio
      recentQA.localAudio !== undefined;

    // Return true if either text or audio answer exists
    return hasTextAnswer || hasAudioAnswer;
  }

  // Prompt for confirmation before update
  const handleUpdate = () => {
    if (!recentQA) {
      setUpdateMessage("No questions to update. Add some questions and answers first.")
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }

    if (!hasChanges()) {
      setUpdateMessage("No changes detected. Make some changes before updating.")
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }

    if (!hasValidAnswer()) {
      setUpdateMessage("Please provide either a text answer or an audio recording.")
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }

    setShowConfirmation(true)
  }

  // Save Q&As to Supabase in batches
  const saveQAToSupabase = async () => {
    setShowConfirmation(false)
    setIsUpdating(true)
    setUpdateStatus('loading')
    setUpdateProgress(0)

    try {
      // Get the client info from dashboard cache
      const clientId = clientInfo?.client_id || '';
      const sector = clientInfo?.sector || '';
      const lang = clientInfo?.lang || 'en';

      // We now only have one item to update (recentQA is a single item, not an array)
      if (!recentQA) {
        throw new Error("No question to update")
      }

      const qa = recentQA; // recentQA is now a single item
      
      // Ensure faq_id exists - it should come from the database
      if (!qa.faq_id) {
        throw new Error("FAQ ID is required for updates. This FAQ may not have been properly loaded from the database.");
      }
      let audioUrl = qa.audio_url;
      let audioFilePath = qa.audio_file_path;
      let audioDuration = qa.audio_duration;

      // Handle audio processing if needed
      if (qa.localAudio) {
        // Find corresponding newly recorded audio blob
        const audioData = newlyRecordedAudio && newlyRecordedAudio.id === qa.id ?
          newlyRecordedAudio : null;

        if (audioData) {
          try {
            // Determine MIME type and extension
            const mimeType = audioData.blob.type || 'audio/webm';
            let fileExtension = 'webm'; // Default extension

            // Get proper file extension based on mime type
            const mimeParts = mimeType.split('/');
            if (mimeParts.length > 1) {
              const subType = mimeParts[1].split(';')[0]; // Handle potential codecs string
              if (subType === 'mp4') fileExtension = 'mp4';
              else if (subType === 'webm') fileExtension = 'webm';
              else if (subType === 'opus') fileExtension = 'opus';
              else if (subType === 'ogg') fileExtension = 'oga'; // Changed to oga for consistency
              else fileExtension = subType; // Use subtype if known
            }

            // Generate unique filename using UUID
            const uniqueFileName = `${uuidv4()}.${fileExtension}`;

            // Get userId for the path (needed for storage RLS policies)
            const { data: userData } = await supabase.auth.getUser();
            if (!userData?.user?.id) {
              throw new Error('User not authenticated. Please log in again.');
            }

            // Create file path with userId
            const targetFilePath = `${userData.user.id}/${uniqueFileName}`;

            // Upload the audio file to Supabase Storage with proper options
            const { data: uploadData, error: uploadError } = await supabase
              .storage
              .from('audios')
              .upload(targetFilePath, audioData.blob, {
                cacheControl: '3600',
                contentType: mimeType,
                upsert: false
              });

            if (uploadError) throw uploadError;

            // Get public URL
            const { data: urlData } = supabase
              .storage
              .from('audios')
              .getPublicUrl(uploadData.path);

            // Set the audio URL and path for the database
            audioUrl = urlData.publicUrl;
            audioFilePath = uploadData.path;
            audioDuration = qa.localAudio.duration;
          } catch (error) {
            console.error('Error uploading audio:', error);
            throw error;
          }
        }
      }

      // Handle product image if selected
      let imageUrl = qa.photo_url;
      let imageName = qa.photo_id;

      if (selectedProduct) {
        // Take URLs from selected product and update photo_url field
        imageUrl = selectedProduct.full_photo_urls;
        imageName = selectedProduct.name;
      } else {
        // If no product selected, check if we're removing an existing photo
        const originalQuestion = questions.find(q => q.id === qa.id);
        if (originalQuestion?.photo_url) {
          // Photo is being removed - clear the photo fields
          imageUrl = undefined;
          imageName = undefined;
        }
      }

      // If we're deleting audio (audio_url is undefined but file path exists)
      // Handle this BEFORE the database update
      if (audioUrl === undefined && qa.audio_file_path) {
        try {
          // Remove the old audio file from storage
          const { error: deleteError, data } = await supabase.storage
            .from('audios')
            .remove([qa.audio_file_path]);

          if (deleteError) {
            console.error('Error deleting old audio:', deleteError);
            // Continue execution even if deletion fails
          } else {
            // If deletion successful, set all audio fields to undefined for database update
            audioUrl = undefined;
            audioDuration = undefined;
            audioFilePath = undefined;
          }
        } catch (error) {
          console.error('Error during audio deletion:', error);
          // Continue execution even if deletion fails
        }
      }

      // Check if we have a text answer or an audio answer
      const hasTextAnswer = qa.answer && qa.answer.trim() !== '';

      // Prepare update object - always use answer_p field
      const updateData: any = {
        updated_at: new Date(), // Add this to trigger the database update timestamp
        audio_url: audioUrl === undefined ? null : audioUrl, // Convert undefined to null for database
        audio_duration: audioDuration === undefined ? null : audioDuration, // Convert undefined to null for database
        audio_file_path: audioFilePath === undefined ? null : audioFilePath, // Convert undefined to null for database
        photo_url: imageUrl === undefined ? null : imageUrl,
        photo_id: imageName === undefined ? null : imageName
      };

      // If we have a text answer and there's an existing audio file, we need to delete it
      // because audio and text answers cannot coexist
      if (hasTextAnswer && qa.audio_file_path && !audioUrl) {
        try {
          // Remove the existing audio file from storage
          const { error: deleteError } = await supabase.storage
            .from('audios')
            .remove([qa.audio_file_path]);

          if (deleteError) {
            console.error('Error deleting audio file when switching to text answer:', deleteError);
            // Continue execution even if deletion fails
          }

          // Set all audio fields to undefined for database update
          audioUrl = undefined;
          audioDuration = undefined;
          audioFilePath = undefined;

          // Update the data object with null values for audio fields
          updateData.audio_url = null;
          updateData.audio_duration = null;
          updateData.audio_file_path = null;

        } catch (error) {
          console.error('Error during audio deletion when switching to text answer:', error);
          // Continue execution even if deletion fails
        }
      }

      // Find the original question to compare changes
      const originalQuestion = questions.find(q => q.id === qa.id);

      // Determine what the final text will be (considering audio/text coexistence logic)
      const finalTextAnswer = audioUrl ? "" : qa.answer;
      const originalTextAnswer = originalQuestion?.answer_p || "";

      // Check if we should ignore text changes (both original and current have audio)
      // This handles transcribed text which is backend-only, not user changes
      const shouldIgnoreTextChanges = originalQuestion?.audio_url && audioUrl;

      // Determine if answer has changed (ignore transcription-only changes)
      const hasAnswerChanged = !shouldIgnoreTextChanges && Boolean(
        // Text content changed
        finalTextAnswer !== originalTextAnswer ||
        // Text was added (original had no text but now has text)
        (!originalTextAnswer && finalTextAnswer?.trim()) ||
        // Text was removed (original had text but now empty)
        (originalTextAnswer && !finalTextAnswer?.trim())
      );

      // Determine if audio has changed
      const hasAudioChanged = Boolean(
        // New recording added
        newlyRecordedAudio !== null ||
        // Audio deleted (was present in original but not in current)
        (originalQuestion?.audio_url && audioUrl === undefined) ||
        // Audio added (wasn't present in original but exists now)
        (!originalQuestion?.audio_url && audioUrl) ||
        // Local audio added
        recentQA?.localAudio !== undefined
      );

      // Check if question was changed
      const hasQuestionChanged = Boolean(
        qa.question && 
        originalQuestion && 
        qa.question !== originalQuestion.question
      );

      // Include question in update if it changed
      if (hasQuestionChanged) {
        updateData.question_p = qa.question;
      }

      // Only update answer_p if answer or audio actually changed
      if (hasAnswerChanged || hasAudioChanged) {
        if (audioUrl) {
          // If audio URL exists, set the text answer to empty string
          // Audio and text answers cannot coexist
          updateData.answer_p = "";
        } else {
          // Update the answer_p field with text
          updateData.answer_p = qa.answer;
        }
      }

      // Determine if photo has changed
      const hasPhotoChanged =
        // Photo added (didn't exist before but exists now)
        (selectedProduct && !originalQuestion?.photo_url) ||
        // Photo removed (existed before but not now)
        (!selectedProduct && originalQuestion?.photo_url) ||
        // Photo changed (different photo ID)
        (selectedProduct && originalQuestion?.photo_url &&
         originalQuestion?.photo_id !== selectedProduct.name);

      // Handle ATM ID fields based on what changed
      if (hasAudioChanged && hasPhotoChanged) {
        // Both audio and photo changed: clear all ATM IDs
        updateData.fb_photo_atmid = null;
        updateData.ig_photo_atmid = null;
        updateData.tg_photo_atmid = null;
        updateData.fb_audio_atmid = null;
        updateData.ig_audio_atmid = null;
        updateData.tg_audio_atmid = null;
      } else if (hasAudioChanged) {
        // Only audio changed: clear audio ATM IDs
        updateData.fb_audio_atmid = null;
        updateData.ig_audio_atmid = null;
        updateData.tg_audio_atmid = null;
      } else if (hasPhotoChanged) {
        // Only photo changed: clear photo ATM IDs
        updateData.fb_photo_atmid = null;
        updateData.ig_photo_atmid = null;
        updateData.tg_photo_atmid = null;
      }

      // Call the update API endpoint
      const updateResponse = await fetch('/api/knowledge/lists', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          faq_id: qa.faq_id,
          updateData: updateData
        })
      });
      
      const updateResult = await updateResponse.json();
      
      if (!updateResponse.ok) {
        throw new Error(updateResult.error || 'Failed to update knowledge item');
      }
      
      // Call webhook if needed (only if not a photo-only change)
      const shouldCallWebhook = !(hasPhotoChanged && !hasAudioChanged && !hasAnswerChanged);
      
      if (shouldCallWebhook) {
        try {
          await fetch('/api/knowledge/lists/webhook', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              faq_id: qa.faq_id,
              client_id: clientId,
              answer: finalTextAnswer,
              sector: sector,
              lang: lang || 'en',
              audio_url: audioUrl || '',
              is_audio: hasAudioChanged
            })
          });
        } catch (error) {
          console.error('Error calling webhook:', error);
          // Don't fail the operation if webhook fails
        }
      }

      // Set progress to 100%
      setUpdateProgress(100);
      
      // Update local state with the new data
      const updatedQuestion = {
        ...qa,
        question: qa.question,
        answer: finalTextAnswer,
        audio_url: audioUrl,
        audio_duration: audioDuration,
        audio_file_path: audioFilePath,
        photo_url: imageUrl,
        photo_id: imageName,
        updated_at: new Date().toISOString()
      };
      updateQuestionInState(updatedQuestion);

      // Clean up object URLs
      if (newlyRecordedAudio) {
        URL.revokeObjectURL(newlyRecordedAudio.url);
      }

      // Clear the recently added item after successful update
      setRecentQA(null)
      setNewlyRecordedAudio(null);
      setSelectedProduct(null);

      // Reset status after a delay
      setTimeout(() => {
        setUpdateStatus('idle')
        setIsUpdating(false)
      }, 1500)

    } catch (err: any) {
      console.error('Error updating knowledge base:', err)
      setUpdateStatus('error')
      setUpdateMessage(err.message || 'Failed to update knowledge base. Please try again.')
      setIsUpdating(false)
    }
  }

  // Helper function to extract URLs from the photo_url JSONB field
  const extractImageUrls = (imageData: any): {firstUrl: string | null, allUrls: string[]} => {
    if (!imageData) {
      return { firstUrl: null, allUrls: [] };
    }

    // If imageData is already a string, return it as the only URL
    if (typeof imageData === 'string') {
      return { firstUrl: imageData, allUrls: [imageData] };
    }

    // Handle case where imageData is an array of URLs
    if (Array.isArray(imageData)) {
      return {
        firstUrl: imageData.length > 0 ? imageData[0] : null,
        allUrls: imageData
      };
    }

    // Handle case where imageData has a url property and perhaps full_urls array
    if (imageData.url) {
      return {
        firstUrl: imageData.url,
        allUrls: imageData.full_urls || [imageData.url]
      };
    }

    // Handle case where imageData has just the full_urls array
    if (imageData.full_urls && Array.isArray(imageData.full_urls)) {
      return {
        firstUrl: imageData.full_urls.length > 0 ? imageData.full_urls[0] : null,
        allUrls: imageData.full_urls
      };
    }

    // Last attempt - try to find any string property that might be a URL
    const possibleUrls = Object.values(imageData).filter(val =>
      typeof val === 'string' && (val.startsWith('http') || val.startsWith('/'))
    ) as string[];

    return {
      firstUrl: possibleUrls.length > 0 ? possibleUrls[0] : null,
      allUrls: possibleUrls
    };
  }

  // Handle delete from Recently Added (just removes from recent items, not from database)
  const handleDelete = () => {
    if (!recentQA) return;

    // Find the original question from the questions array
    const originalQuestion = questions.find(q => q.id === recentQA.id);

    if (!originalQuestion) {
      // If we can't find the original (unlikely), just close the edit view
      setRecentQA(null);
      return;
    }

    // Check for all types of changes
    const hasTextChanges = recentQA.answer !== originalQuestion.answer;
    const hasAudioChanges =
      // New recording added
      newlyRecordedAudio !== null ||
      // Currently recording
      isRecording ||
      // Audio deleted (was present in original but not in current)
      (originalQuestion.audio_url && !recentQA.audio_url) ||
      // Local audio added
      recentQA.localAudio !== undefined;

    // Check for photo changes
    const hasPhotoChanges = 
      // Photo added or changed
      (selectedProduct !== null &&
        (
          // No photo originally but now has one
          (!originalQuestion.photo_url && selectedProduct) ||
          // Photo changed (different name)
          (originalQuestion.photo_id !== selectedProduct.name)
        )
      ) ||
      // Photo removed (had photo originally but now doesn't)
      (originalQuestion.photo_url && !selectedProduct);

    // Currently editing text
    const isCurrentlyEditing = editingItem !== null;

    // Determine if any changes have been made
    const hasChanges = hasTextChanges || hasAudioChanges || hasPhotoChanges || isCurrentlyEditing;

    if (hasChanges) {
      // Show confirmation before canceling
      setShowCancelConfirmation(true);
    } else {
      // No changes, proceed with delete
      setRecentQA(null);
    }
  }

  // Handle cancel confirmation - proceed with deletion
  const confirmCancel = () => {
    setShowCancelConfirmation(false);
    setRecentQA(null);

    // Also clean up any ongoing operations
    if (isRecording) {
      cancelRecording();
    }
    if (newlyRecordedAudio) {
      URL.revokeObjectURL(newlyRecordedAudio.url);
      setNewlyRecordedAudio(null);
    }
    if (editingItem) {
      setEditingItem(null);
    }
  }



  // Handle removing image from an item in the update section
  const handleRemoveImage = () => {
    setRecentQA(null)
  }

  // Show delete confirmation
  const showDeleteConfirmation = (id: number, isAudio: boolean, audio_file_path?: string) => {
    setDeleteConfirm({ id, isAudio, audio_file_path })
  }

  // Handle delete from database
  const handleDeleteFromDatabase = async () => {
    if (!deleteConfirm) return

    setIsDeleting(true)

    try {
      const { id, isAudio, audio_file_path } = deleteConfirm

      // If it's an audio entry, delete the audio file from storage first
      if (isAudio && audio_file_path) {
        const { error: storageError } = await supabase
          .storage
          .from('audios')
          .remove([audio_file_path])

        if (storageError) {
          console.error('Error deleting audio file:', storageError)
          throw new Error('Failed to delete audio file. Please try again.')
        }
      }

      // Add a small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Get the FAQ item to delete
      const faqToDelete = questions.find(q => q.id === id);
      if (!faqToDelete?.faq_id) {
        throw new Error('FAQ not found or missing faq_id');
      }

      // Delete the database record via API
      const deleteResponse = await fetch(`/api/knowledge/lists?faq_id=${faqToDelete.faq_id}`, {
        method: 'DELETE'
      })

      const deleteResult = await deleteResponse.json()

      if (!deleteResponse.ok) {
        console.error('Error deleting knowledge item:', deleteResult.error)
        throw new Error(deleteResult.error || 'Failed to delete question. Please try again.')
      }

      // Use incremental update instead of refetching all data
      removeQuestionFromState(id);

      // Update FAQ count in dashboard cache immediately
      // Calculate new count based on the current questions array after removal
      const newFaqCount = Math.max(questions.length - 1, 0);
      updateFaqCountInCache(newFaqCount);

      // Update success message
      setUpdateStatus('success')
      setUpdateMessage('Question deleted successfully.')

      // Reset status after a delay
      setTimeout(() => {
        setUpdateStatus('idle')
      }, 1500)

    } catch (error: any) {
      console.error('Error in deletion:', error)
      setUpdateStatus('error')
      setUpdateMessage(error.message || 'Failed to delete. Please try again.')
    } finally {
      setIsDeleting(false)
      setDeleteConfirm(null)
    }
  }



  // Refs for virtual scrolling and navigation
  const knowledgeBaseRef = useRef<HTMLDivElement>(null)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const databaseSectionRef = useRef<HTMLDivElement>(null)

  // Global scroll handler for virtual scrolling
  const handleGlobalScroll = useCallback(() => {
    if (!knowledgeBaseRef.current || filteredQuestions.length === 0) return

    const knowledgeBaseRect = knowledgeBaseRef.current.getBoundingClientRect()
    const viewportHeight = window.innerHeight

    // Check if the knowledge base section is visible in viewport
    if (knowledgeBaseRect.bottom < 0 || knowledgeBaseRect.top > viewportHeight) {
      return // Knowledge base is not visible, no need to update
    }

    // Calculate the content area within the knowledge base (accounting for padding and headers)
    const contentStartY = knowledgeBaseRect.top + 120 // Account for section padding and headers
    const viewportTop = 0
    const viewportBottom = viewportHeight

    // Calculate which items should be visible based on viewport intersection
    // An item is visible if any part of it intersects with the viewport
    let newStartIndex = 0
    let newEndIndex = Math.min(visibleItemsCount - 1, filteredQuestions.length - 1)

    // Only calculate virtual scrolling if content area is partially visible
    if (contentStartY < viewportBottom) {
      // Calculate scroll offset relative to the content start
      const scrollOffset = Math.max(0, viewportTop - contentStartY)

      // Calculate start index - show item if any part is visible (not completely scrolled out)
      newStartIndex = Math.max(0, Math.floor(scrollOffset / itemHeight))

      // Calculate end index - show enough items to fill viewport plus buffer
      const visibleHeight = Math.min(viewportBottom - Math.max(viewportTop, contentStartY), knowledgeBaseRect.bottom - Math.max(viewportTop, contentStartY))
      const itemsNeeded = Math.ceil(visibleHeight / itemHeight) + 2 // +2 for buffer
      newEndIndex = Math.min(newStartIndex + Math.max(itemsNeeded, visibleItemsCount) - 1, filteredQuestions.length - 1)
    }

    setVisibleStartIndex(newStartIndex)
    setVisibleEndIndex(newEndIndex)
  }, [filteredQuestions.length, itemHeight, visibleItemsCount])

  // Effect to add/remove global scroll listener
  useEffect(() => {
    window.addEventListener('scroll', handleGlobalScroll, { passive: true })
    window.addEventListener('resize', handleGlobalScroll, { passive: true })

    // Initial calculation
    handleGlobalScroll()

    return () => {
      window.removeEventListener('scroll', handleGlobalScroll)
      window.removeEventListener('resize', handleGlobalScroll)
    }
  }, [handleGlobalScroll])

  // Effect to reset virtual scroll when search changes
  useEffect(() => {
    setVisibleStartIndex(0)
    setVisibleEndIndex(Math.min(visibleItemsCount - 1, filteredQuestions.length - 1))
    // Trigger recalculation after state update
    setTimeout(handleGlobalScroll, 0)
  }, [searchQuery, filteredQuestions.length, handleGlobalScroll, visibleItemsCount])

  // Scroll to database section
  const scrollToDatabase = () => {
    if (databaseSectionRef.current) {
      databaseSectionRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  }

  // Virtual scrolling helper functions
  const getItemStyle = (index: number) => ({
    position: 'absolute' as const,
    top: index * itemHeight,
    left: 0,
    right: 0,
    height: itemHeight,
  })

  const getTotalHeight = () => filteredQuestions.length * itemHeight

  // Format date string
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })
  }

  // Add effect to focus textarea and set cursor at the end when editing
  useEffect(() => {
    if (editingItem && textareaRef.current && !hasFocusedInput) {
      // Check if device is desktop (not mobile/tablet) for auto-focus behavior
      const isDesktop = window.innerWidth >= 1024 && !('ontouchstart' in window)

      if (isDesktop) {
        // On desktop: auto-focus and set cursor at the end
        textareaRef.current.focus()
        const length = textareaRef.current.value.length
        textareaRef.current.setSelectionRange(length, length)
        setHasFocusedInput(true)
      }
      // On mobile/tablet: let user manually tap to position cursor and handle keyboard
      // Don't set hasFocusedInput to true yet - wait for user's first click
    }
  }, [editingItem, hasFocusedInput])

  // Reset focus tracking when modal closes
  useEffect(() => {
    if (!editingItem) {
      setHasFocusedInput(false);
    }
  }, [editingItem]);



  const handleStartEdit = (id: number, field: 'question' | 'answer', value: string) => {
    setEditingItem({ id, field, value })
    setHasFocusedInput(false)
    // Focus and set cursor at end will happen in useEffect
  }

  const handleSaveEdit = () => {
    if (editingItem) {
      // Always save the edit, even if field is empty
      if (recentQA) {
                  setRecentQA({
            ...recentQA,
            [editingItem.field]: editingItem.value
          });
      }
    }
    setEditingItem(null)
  }

  // Enhanced Unified Audio Playback System
  const playUnifiedAudio = useCallback((audioUrl: string, duration: number, audioType: 'existing' | 'new', identifier?: number) => {
    try {
      // Stop any currently playing audio first (conflict resolution)
      if (audioPlayback) {
        stopAudioPlayback();
      }

      // Validate inputs
      if (!audioUrl || duration < 0) {
        console.error('Invalid audio parameters:', { audioUrl, duration });
        return null;
      }

      // Initialize audio and detect Safari
      const { isSafari } = initializeAudio();

      const audioId = identifier || Date.now();

      if (isSafari && audioContext.current) {
        // Safari: Use Web Audio API with stereo routing
        return playWebAudioUnified(audioUrl, duration, audioType, audioId);
      } else {
        // Other browsers: Use HTML5 Audio
        return playHTML5Unified(audioUrl, duration, audioType, audioId);
      }
    } catch (error) {
      console.error('Error in playUnifiedAudio:', error);
      return null;
    }
  }, [audioPlayback, stopAudioPlayback, initializeAudio]);

  // Web Audio API Implementation (Safari)
  const playWebAudioUnified = async (audioUrl: string, duration: number, audioType: 'existing' | 'new', identifier?: number) => {
    try {
      // Initialize AudioContext if not already created
      if (!audioContext.current) {
        audioContext.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      }

      // Fetch and decode audio data
      const response = await fetch(audioUrl);
      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer = await audioContext.current.decodeAudioData(arrayBuffer);

      // Create source and channel merger for stereo output
      const source = audioContext.current.createBufferSource();
      source.buffer = audioBuffer;
      const channelMerger = audioContext.current.createChannelMerger(2);

      // Stereo routing setup
      source.connect(channelMerger, 0, 0); // Route to left
      source.connect(channelMerger, 0, 1); // Route to right
      channelMerger.connect(audioContext.current.destination);

      // Duration with buffer
      const actualDuration = Math.floor(audioBuffer.duration) + 0.5; // Add 500ms buffer

      // Set up time tracking for UI updates
      const startTime = audioContext.current.currentTime;
      let animationFrame: number;

      const updateTime = () => {
        const elapsed = Math.floor(audioContext.current!.currentTime - startTime);
        const remaining = Math.max(0, Math.floor(actualDuration - elapsed));
        
        // Update unified state
        setAudioPlayback(prev => prev ? { ...prev, remainingTime: remaining } : null);

        if (remaining > 0) {
          animationFrame = requestAnimationFrame(updateTime);
        }
      };

      // Set unified playing state
      setAudioPlayback({
        id: identifier || Date.now(),
        audio: source as any,
        duration: duration,
        remainingTime: duration,
        type: audioType
      });
      audioRef.current = source as any;

      // Start playback and time tracking
      source.start(0);
      updateTime();

      // Event-driven cleanup
      source.onended = () => {
        cancelAnimationFrame(animationFrame);
        
        // Reset unified state immediately
        setAudioPlayback(null);
        audioRef.current = null;
      };

      return source; // Return source for potential stopping
    } catch (error) {
      console.error('Web Audio API playback failed:', error);
      // Fallback to HTML5 Audio if Web Audio API fails
      playHTML5Unified(audioUrl, duration, audioType, identifier);
    }
  };

  // HTML5 Audio Implementation (Other Browsers)
  const playHTML5Unified = (audioUrl: string, duration: number, audioType: 'existing' | 'new', identifier?: number) => {
    try {
      const audio = new Audio(audioUrl);
      
      // Duration with buffer
      const bufferedDuration = duration + 0.5; // Add 500ms buffer for smoother animation
      
      let animationFrame: number;
      
      const updateTime = () => {
        const audioCurrentTime = audio.currentTime;
        const remaining = Math.max(0, Math.floor(bufferedDuration - audioCurrentTime));
        
        // Update unified state
        setAudioPlayback(prev => prev ? { ...prev, remainingTime: remaining } : null);

        if (remaining > 0 && !audio.paused) {
          animationFrame = requestAnimationFrame(updateTime);
        }
      };

      // Event-driven cleanup
      const endedHandler = () => {
        cancelAnimationFrame(animationFrame);
        
        // Reset unified state immediately
        setAudioPlayback(null);
        audioRef.current = null;
      };

      audio.addEventListener('ended', endedHandler);
      audio.addEventListener('loadeddata', () => {
        audio.play().then(() => {
          updateTime();
          
          // Set unified playing state
          setAudioPlayback({
            id: identifier || Date.now(),
            audio: audio as any,
            duration: duration,
            remainingTime: duration,
            type: audioType
          });
          audioRef.current = audio as any;
        }).catch(error => {
          console.error("HTML5 Audio playback failed:", error);
        });
      });

      return audio;
    } catch (error) {
      console.error('HTML5 Audio playback failed:', error);
    }
  };



  // Simplified handleAudioPlayback using utility functions
  const handleAudioPlayback = useCallback(async (id: number, audioUrl?: string, audioDuration?: number) => {
    if (!audioUrl) return;

    // If already playing this audio, pause/stop it
    if (audioPlayback?.id === id) {
      pauseAudioPlayback();
      return;
    }

    const durationInSeconds = audioDuration || 0;

    // Use unified audio playback system (auto-stops other audio)
    playUnifiedAudio(audioUrl, durationInSeconds, 'existing', id);
  }, [audioPlayback, pauseAudioPlayback, playUnifiedAudio]);

  // Add function to clear selected product
  const handleClearSelectedProduct = () => {
    setSelectedProduct(null)
  }

  // Add function to search photos from Supabase
  const searchPhotos = async (query: string) => {
    if (!query.trim()) {
      setPhotoSearchResults([])
      setShowPhotoResults(false)
      return
    }

    setIsSearching(true)
    try {
      // Use the cached photos data from usePhotosData hook
      if (photosData) {
        const filteredPhotos = photosData
          .filter(photo => 
            photo && photo.photo_id && photo.photo_id.toLowerCase().includes(query.toLowerCase())
          )
          .slice(0, 5)

        setPhotoSearchResults(filteredPhotos)
        setShowPhotoResults(filteredPhotos.length > 0)
      } else {
        // If no cached data, fetch from API
        const response = await fetch('/api/knowledge/photos')
        const data = await response.json()
        
        if (data.photos) {
          const filteredPhotos = data.photos
            .filter((photo: any) => 
              photo && photo.photo_id && photo.photo_id.toLowerCase().includes(query.toLowerCase())
            )
            .slice(0, 5)

          setPhotoSearchResults(filteredPhotos)
          setShowPhotoResults(filteredPhotos.length > 0)
        }
      }
    } catch (error) {
      console.error('Error searching photos:', error)
      setPhotoSearchResults([])
      setShowPhotoResults(false)
    } finally {
      setIsSearching(false)
    }
  }

  // Add function to clear photo search
  const clearPhotoSearch = () => {
    setPhotoSearchResults([]);
    setShowPhotoResults(false);
    setPhotoSearchQuery('');
  };

  // Add function to handle photo search input
  const handlePhotoSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setPhotoSearchQuery(query)
    if (query.trim()) {
      searchPhotos(query)
    } else {
      clearPhotoSearch()
    }
  }

  // Add function to select a photo
  const handleSelectPhoto = (photo: {
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }) => {
    // Get first photo URL as thumbnail for display, but store the full array
    const thumbnail = photo.photo_url && photo.photo_url.length > 0 ? photo.photo_url[0] : null

    // Show loading animation
    setIsPhotoLoading(true)
    
    // Clear search results and hide dropdown
    clearPhotoSearch()
    setPhotoSearchQuery('')

    // Add a small delay to show the loading animation
    setTimeout(() => {
      // Set the selected photo
      setSelectedProduct({
        prod_id: `photo_${photo.id}`,
        name: photo.photo_id,
        photo_url: thumbnail,
        full_photo_urls: photo.photo_url
      })

      // Hide loading animation after a short delay
      setTimeout(() => {
        setIsPhotoLoading(false)
      }, 50)
    }, 100)
  }




  // Close photo search results when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchResultsRef.current && !searchResultsRef.current.contains(event.target as Node)) {
        setShowPhotoResults(false)
      }
    }

    if (showPhotoResults) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showPhotoResults])

  // Handle audio delete confirmation
  const handleConfirmAudioDelete = (qaId: number) => {
    setConfirmAudioDelete(qaId)
  }

  // Handle audio delete
  const handleDeleteAudio = () => {
    if (confirmAudioDelete === null) {
      return;
    }

    setRecentQA(prev => {
      if (!prev || prev.id !== confirmAudioDelete) return prev;

      // Create a copy of prev with the same type
      const updatedQA = { ...prev };
      // Modify properties - using undefined instead of null for type compatibility
      updatedQA.audio_url = undefined;
      updatedQA.audio_duration = undefined;
      updatedQA.localAudio = undefined;

      return updatedQA;
    });

    setConfirmAudioDelete(null);

    // If the deleted audio is the newly recorded one, clear that record too
    if (newlyRecordedAudio && newlyRecordedAudio.id === confirmAudioDelete) {
      // Revoke the object URL to prevent memory leaks
      URL.revokeObjectURL(newlyRecordedAudio.url);
      setNewlyRecordedAudio(null);
    }
  }

  // Check and request audio permission
  const checkAudioPermission = async (): Promise<boolean> => {
    try {
      setIsCheckingPermission(true)
      setAudioPermissionState('requesting')
      
      // Check if permissions API is available
      if ('permissions' in navigator) {
        const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName })
        
        if (permission.state === 'granted') {
          setAudioPermissionState('granted')
          return true
        } else if (permission.state === 'denied') {
          setAudioPermissionState('denied')
          return false
        }
      }
      
      // If permissions API not available or state is 'prompt', request permission
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      
      // Permission granted - clean up the stream immediately
      stream.getTracks().forEach(track => track.stop())
      setAudioPermissionState('granted')
      return true
      
    } catch (error) {
      console.error('Audio permission error:', error)
      setAudioPermissionState('denied')
      return false
    } finally {
      setIsCheckingPermission(false)
    }
  }

  // Handle start recording for a specific item with permission check
  const startRecordingForItem = async (qaId: number) => {
    // Check if permission is already granted
    if (audioPermissionState === 'granted') {
      // Permission already exists - show modal and allow immediate recording
      setRecordingItem(qaId)
      setIsRecordingAudio(true)
      setRecordingDuration(0)
      return
    }
    
    // Check/request permission first
    const hasPermission = await checkAudioPermission()
    
    if (hasPermission) {
      // Permission granted - show modal for recording
      setRecordingItem(qaId)
      setIsRecordingAudio(true)
      setRecordingDuration(0)
    } else {
      // Permission denied - show error message
      alert('Microphone access is required to record audio. Please allow microphone access in your browser settings and try again.')
    }
  }

  const startRecording = async () => {
    if (isRecording) return; // Don't start if already recording

    // Cancel any existing audio playback
    if (audioPlayback) {
      stopAudioPlayback();
    }

    try {
      // Clear previous recording data if any
      if (newlyRecordedAudio) {
        URL.revokeObjectURL(newlyRecordedAudio.url);
        setNewlyRecordedAudio(null);
      }

      // Reset timer counter to 0
      setRecordingDuration(0);

      // Enhanced audio constraints for better quality
      const audioConstraints = {
        audio: {
          echoCancellation: false,    // Keep as is
          noiseSuppression: true,
          autoGainControl: false,
          sampleRate: 48000,
          sampleSize: 16,
          channelCount: 1  // Mono for speech clarity
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(audioConstraints);
      
      // Add initialization delay if permission was just granted to ensure proper setup
      if (audioPermissionState === 'requesting') {
        console.log('Adding initialization delay for first-time permission grant...');
        await new Promise(resolve => setTimeout(resolve, 1500)); // 1.5 second delay
      }
      
      setAudioStream(stream);

      // Get browser and mobile detection
      const { isMobile, isSafari } = initializeAudio();

      // Web Audio API volume processing for cross-browser compatibility
      let processedStream = stream;
      
      try {
        const recordingAudioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
        const sourceNode = recordingAudioContext.createMediaStreamSource(stream);
        const gainNode = recordingAudioContext.createGain();
        
        // Browser-specific recording gain values
        if (isSafari) {
          // Safari (desktop + mobile): 5x volume boost for storage consistency
          gainNode.gain.value = 5.0;
        } else {
          // Others (desktop + mobile): 2x volume boost for storage consistency
          gainNode.gain.value = 2.0;
        }
        
        const destination = recordingAudioContext.createMediaStreamDestination();
        sourceNode.connect(gainNode);
        gainNode.connect(destination);
        processedStream = destination.stream;
      } catch (error) {
        console.warn('Volume processing failed, using original stream:', error);
        processedStream = stream;
      }

      // Prefer high-quality codec options (copied from knowledge/page.tsx)
      const supportedTypes = [
        'audio/mp4;codecs=mp4a.40.2', // AAC encoding
        'audio/mp4',
        'audio/webm;codecs=opus', // Best quality for voice
        'audio/webm',
      ];

      let selectedMimeType = '';
      for (const type of supportedTypes) {
        if (MediaRecorder.isTypeSupported(type)) {
          selectedMimeType = type;
          break;
        }
      }

      if (!selectedMimeType) {
        console.error("[startRecording] No supported mimeType found for MediaRecorder!");
        alert("Your browser doesn't support audio recording in a compatible format.");
        stream.getTracks().forEach(track => track.stop());
        setAudioStream(null);
        return;
      }

      const chunks: Blob[] = [];

      // Set higher bitrate for better quality (copied from knowledge/page.tsx)
      const recorderOptions = {
        mimeType: selectedMimeType,
        audioBitsPerSecond: 192000  // 128kbps for good voice quality
      };

      const recorder = new MediaRecorder(processedStream, recorderOptions);
      setMediaRecorder(recorder);

      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      recorder.onstop = () => {

        if (recordingItem === null) {
          console.error("Recording stopped but no recordingItem ID was set.");
          // Clean up stream just in case
          if (stream) {
            stream.getTracks().forEach(track => track.stop());
            setAudioStream(null);
          }

          // Close modal even if there was an error
          setTimeout(() => {
            setIsProcessingAudio(false);
            setIsRecordingAudio(false);
          }, 1000);

          return; // Exit if we don't know which item this recording belongs to
        }

        // Use the same selectedMimeType when creating the Blob
        const audioBlob = new Blob(chunks, { type: selectedMimeType });

        if (audioBlob.size === 0) {
          console.error("Created audio blob is empty!");
          alert("Failed to record audio. Please try again.");

          // Close modal after a delay
          setTimeout(() => {
            setIsProcessingAudio(false);
            setIsRecordingAudio(false);
          }, 1000);
          return;
        }

        const audioUrl = URL.createObjectURL(audioBlob);

        // Get the final recording duration - try to get it from the mediaRecorder property we set
        // or fall back to the current recordingDuration state
        const finalDuration = (recorder as any).finalRecordingDuration || Math.max(recordingDuration, 1);


        // Update the specific QA item with the new local audio URL and duration
        if (recordingItem) {
          setRecentQA(prev => {
            if (!prev) return prev;

            return {
              ...prev,
              localAudio: { url: audioUrl, duration: finalDuration },
              answer: "" // Use an empty answer string to avoid displaying redundant text
            };
          });
        }

        // Store the newly recorded audio details separately for immediate playback/management
        const newAudio = { id: recordingItem, url: audioUrl, duration: finalDuration, blob: audioBlob };
        setNewlyRecordedAudio(newAudio);

        // Clean up stream
        if (stream) {
          stream.getTracks().forEach(track => track.stop());
          setAudioStream(null);
        }

        // Clear recording state but keep track of the item ID
        setIsRecording(false);
        if (recordingTimer) clearInterval(recordingTimer);
        setRecordingTimer(null);

        // Keep isProcessingAudio true during the delay
        // Close modal after a delay to show completion
        setTimeout(() => {
          setIsProcessingAudio(false); // Set this to false just before closing the modal
          setIsRecordingAudio(false); // This will close the modal
        }, 1000);
      };

      recorder.start();
      setIsRecording(true);

      // Stop any existing timer first
      if (recordingTimer) {
        clearInterval(recordingTimer);
      }

      // Start new timer
      const timer = setInterval(() => {
        setRecordingDuration(prev => {
          // Update the timer by incrementing previous value
          const newDuration = prev + 1;
          return newDuration;
        });
      }, 1000);
      setRecordingTimer(timer);

    } catch (error) {
      console.error('Error accessing microphone or starting recording:', error);
      alert('Unable to access microphone. Please check browser permissions.');
      // Ensure cleanup if error occurs during setup
      if (audioStream) {
        audioStream.getTracks().forEach(track => track.stop());
        setAudioStream(null);
      }
      setIsRecording(false);
      setRecordingItem(null);
      if (recordingTimer) clearInterval(recordingTimer);
      setRecordingTimer(null);
    }
  };

  const stopRecording = () => {

    // Immediately capture the current duration before stopping
    const capturedDuration = recordingDuration;

    if (mediaRecorder && isRecording) {
      setIsProcessingAudio(true); // Indicate processing is starting

      try {
        // Stop any existing timer to prevent duration from increasing
        if (recordingTimer) {
          clearInterval(recordingTimer);
          setRecordingTimer(null);
        }

        // Store the final duration in a ref to make sure we can access it in onstop handler
        const finalDuration = Math.max(capturedDuration, 1);

        // Expose duration on mediaRecorder to access it in onstop
        (mediaRecorder as any).finalRecordingDuration = finalDuration;

        mediaRecorder.stop();
        // The onstop handler will manage the rest: blob creation, state updates, and modal closing
      } catch (error) {
        console.error("Error stopping recorder:", error);
        // Fallback in case of error - close modal after delay
        setTimeout(() => {
          setIsProcessingAudio(false);
          setIsRecordingAudio(false);
        }, 1000);
      }
    } else {
      console.warn("stopRecording called but no active recorder or not recording.");
      // Force cleanup if something went wrong
      cancelRecording();
    }
  };

  // Cancel recording
  const cancelRecording = () => {
    // Stop recording if active
    if (mediaRecorder && isRecording) {
      try {
        mediaRecorder.stop();
      } catch (e) {
        console.error("Error stopping media recorder during cancel:", e);
      }

      // Stop all audio tracks
      if (audioStream) {
        audioStream.getAudioTracks().forEach(track => track.stop());
      }

      // Clear timer
      if (recordingTimer) {
        clearInterval(recordingTimer);
        setRecordingTimer(null);
      }
    }

    // Reset recording state
    setIsRecordingAudio(false);
    setIsProcessingAudio(false);
    setRecordingItem(null);
    setIsRecording(false);
    setRecordingDuration(0);
    setAudioStream(null);
    setMediaRecorder(null);
  };

  // Close audio recording modal when clicking outside - DISABLED per request
  /*
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (audioRecordModalRef.current && !audioRecordModalRef.current.contains(event.target as Node)) {
        cancelRecording()
      }
    }

    if (isRecordingAudio) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isRecordingAudio])
  */

  // When component unmounts, clean up any object URLs
  useEffect(() => {
    return () => {
      if (localAudioBlob) {
        URL.revokeObjectURL(localAudioBlob.url)
      }
    }
  }, [])

  // Simplified handleNewAudioPlayback using utility functions
  const handleNewAudioPlayback = useCallback(async (id: number, audioUrl: string, audioDuration: number) => {
    if (!audioUrl) {
      console.error("No audio URL provided for playback");
      return;
    }

    // If already playing this new audio, pause/stop it
    if (audioPlayback?.id === id && audioPlayback?.type === 'new') {
      pauseAudioPlayback();
      return;
    }

    const durationInSeconds = audioDuration && audioDuration > 0 ? audioDuration : 0;

    // Use unified audio playback system (auto-stops other audio)
    const audioElement = playUnifiedAudio(audioUrl, durationInSeconds, 'new', id);
    if (audioElement) {
      audioRef.current = audioElement as any;
    }
  }, [audioPlayback, pauseAudioPlayback, playUnifiedAudio]);


  // Unified audio cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupAudio();
    };
  }, [cleanupAudio]);

  // Add debug logging for tracking audio duration
  useEffect(() => {
    // Log when recordingDuration changes to track its value
    if (isRecording) {
    }

    // Log newlyRecordedAudio when it changes
    if (newlyRecordedAudio) {
    }

    // Log localAudio for current item in recentQA
    if (recentQA && recentQA.localAudio) {
      const qa = recentQA;
      if (qa.localAudio) {
      }
    }
  }, [recordingDuration, newlyRecordedAudio, recentQA, isRecording]);

  // Handle viewing an image in gallery modal
  const handleViewImage = (imageData: any) => {
    const { allUrls } = extractImageUrls(imageData);
    if (allUrls.length > 0) {
      setImageGallery({
        urls: allUrls,
        currentIndex: 0
      });
    } else {
      // Show a notification or alert that there are no images to display
      setUpdateStatus('error');
      setUpdateMessage('No images available for this item.');
      setTimeout(() => setUpdateStatus('idle'), 3000);
    }
  }

  // Keyboard navigation for image gallery (arrow keys and escape)
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (!imageGallery) return;

      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        const prevIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
        setImageGallery({ ...imageGallery, currentIndex: prevIndex });
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        const nextIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
        setImageGallery({ ...imageGallery, currentIndex: nextIndex });
      } else if (event.key === 'Escape') {
        event.preventDefault();
        setImageGallery(null);
      }
    }

    if (imageGallery) {
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    }
  }, [imageGallery]);

  // Escape key functionality for View Modal
  useEffect(() => {
    function handleViewModalKeyDown(event: KeyboardEvent) {
      if (!viewingItem) return;

      if (event.key === 'Escape') {
        event.preventDefault();
        setViewingItem(null);
      }
    }

    if (viewingItem) {
      document.addEventListener('keydown', handleViewModalKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleViewModalKeyDown);
    }
  }, [viewingItem]);

  // Disable page scroll when gallery is open
  useEffect(() => {
    if (imageGallery) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [imageGallery]);

  // Disable page scroll when any popup/modal is open
  useEffect(() => {
    const hasAnyPopup = viewingItem || editingItem || showConfirmation || deleteConfirm ||
                       updateStatus === 'loading' || updateStatus === 'success' || updateStatus === 'error' ||
                       isRecordingAudio || confirmAudioDelete || showCancelConfirmation;

    if (hasAnyPopup) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [viewingItem, editingItem, showConfirmation, deleteConfirm, updateStatus, isRecordingAudio, confirmAudioDelete, showCancelConfirmation]);

  // Handle touch events for swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd || !imageGallery) return;

    const minSwipeDistance = 50; // Minimum distance in pixels to be considered a swipe
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe) {
      // Handle left swipe (next image)
      const nextIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
      setImageGallery({ ...imageGallery, currentIndex: nextIndex });
    }

    if (isRightSwipe) {
      // Handle right swipe (previous image)
      const prevIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
      setImageGallery({ ...imageGallery, currentIndex: prevIndex });
    }

    // Reset touch coordinates
    setTouchStart(null);
    setTouchEnd(null);
  };

  return (
    <div className={`min-h-screen ${themeConfig.pageBackground} flex flex-col relative pb-16`}>
      {/* Background effects (only for dark theme) */}
      {themeConfig.backgroundEffects}

      {/* Modern Glassmorphism Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.headerBackground} rounded-2xl px-4 py-3 border ${themeConfig.headerBorder} ${themeConfig.headerHoverBorder} transition-all duration-300 overflow-hidden`}
          >


            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <LinkButton 
                href="/dashboard" 
                variant="ghost" 
                className="p-0 hover:bg-transparent active:scale-95"
              >
                <img
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 group-hover:scale-105"
                />
              </LinkButton>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Content header with title and back button */}
          <div className="mb-12">
            <div className="flex items-center justify-between mb-2">
              <LinkButton
                href="/dashboard/knowledge"
                variant="secondary"
                size="sm"
                className="inline-flex items-center text-sm"
                leftIcon={
                  <svg
                    className="w-4 h-4 -ml-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                }
              >
                {t('back')}
              </LinkButton>

              <h1 className={`text-2xl md:text-3xl font-extrabold font-title ${themeConfig.textPrimary}`}>
                {t('business_insight')}
              </h1>

              {/* Empty div for balanced spacing */}
              <div className="w-10"></div>
            </div>
          </div>

          {/* View Modal */}
          {viewingItem && (
            <div
              className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/20'} backdrop-blur-sm flex items-center justify-center z-50`}
            >
              <div
                ref={viewModalRef}
                className={`relative rounded-2xl p-6 w-full max-w-lg mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-white/[0.075] border-white/20'}`}
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                <div className="relative z-10">
                  {/* Close button (X) in the top-right corner */}
                  <button
                    onClick={() => setViewingItem(null)}
                    className={`absolute top-0 right-0 p-1 rounded-full transition-colors z-20 border ${theme === 'light' ? 'bg-gray-100 hover:bg-red-100 text-gray-600 hover:text-red-600 border-gray-300 hover:border-red-300' : 'bg-black/40 hover:bg-jade-purple text-white/60 hover:text-white border-white/20'}`}
                    aria-label="Close"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>

                <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                  {viewingItem.field === 'question' ? t('question') : t('reply')} 
                  <span className={`${themeConfig.textSecondary} ml-1`}>
                    #{questions.findIndex(q => 
                      viewingItem.field === 'question' ? q.question === viewingItem.value : q.answer === viewingItem.value
                    ) + 1}
                  </span>
                </h3>
                <div className={`${theme === 'light' ? 'bg-white' : 'bg-black/30'} border-2 ${themeConfig.inputBorder} rounded-lg p-4 ${themeConfig.textPrimary} mb-4 min-h-[150px] max-h-[200px] overflow-y-auto whitespace-pre-wrap`}>
                  {/* Only show text value if not viewing an answer with audio */}
                  {!(viewingItem.field === 'answer' && questions.find(q => q.answer === viewingItem.value)?.audio_url) && viewingItem.value}

                  {/* Display audio player if viewing an answer with audio_url */}
                  {viewingItem.field === 'answer' &&
                   questions.find(q => q.answer === viewingItem.value)?.audio_url && (
                    <div className={`mt-1 ${themeConfig.textPrimary}`}>
                      <div className="flex items-center">
                        <button
                          className="mr-4 p-2 rounded-full bg-zinc-700 hover:bg-zinc-600 transition-colors"
                          onClick={() => {
                            const qa = questions.find(q => q.answer === viewingItem.value);
                            if (qa?.audio_url) {
                              handleAudioPlayback(qa.id, qa.audio_url, qa.audio_duration);
                            }
                          }}
                        >
                          {/* Play/Pause Icon */}
                          {audioPlayback?.id === questions.find(q => q.answer === viewingItem.value)?.id && audioPlayback?.type === 'existing' ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          )}
                        </button>
                        <span>
                          {t('audio')}: {audioPlayback?.id === questions.find(q => q.answer === viewingItem.value)?.id && audioPlayback?.type === 'existing' && audioPlayback?.remainingTime !== undefined
                            ? `${audioPlayback?.remainingTime}s`
                            : `${questions.find(q => q.answer === viewingItem.value)?.audio_duration || '0'}s`}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
                </div>
              </div>
            </div>
          )}

          {/* Edit Modal */}
          {editingItem && (
            <div
              className={`fixed inset-0 ${theme === 'light' ? 'bg-black/20' : 'bg-black/20'} backdrop-blur-sm flex items-center justify-center z-50`}
            >
              <div
                ref={modalRef}
                className={`relative ${theme === 'light' ? 'bg-white' : 'bg-jade-purple-dark/[0.3]'} rounded-2xl p-6 w-full max-w-lg mx-4 border-2 ${theme === 'light' ? 'border-gray-200' : 'border-white/20'} overflow-hidden shadow-xl`}
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                <div className="relative z-10" data-modal-content>
                  {/* Close button (X) */}
                  <button
                    className={`absolute top-0 right-0 p-1 rounded-full ${theme === 'light' ? 'bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 border-gray-300' : 'bg-black/40 hover:bg-jade-purple text-white/60 hover:text-white border-white/20'} border transition-colors`}
                    onClick={() => setEditingItem(null)}
                    aria-label="Close"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>

                  <h3 className={`text-xl ${themeConfig.textPrimary} font-bold mb-4 font-title text-center`}>
                    {editingItem.field === 'question' ? t('kb_edit_question') : t('edit_reply')}
                  </h3>
                  <textarea
                    ref={textareaRef}
                    value={editingItem.value}
                    onChange={(e) => {
                      // Only update if value actually changed
                      if (e.target.value !== editingItem.value) {
                        setEditingItem(prev => prev ? {...prev, value: e.target.value} : null);
                      }
                    }}
                    onClick={() => {
                      // On mobile: only position cursor at end on FIRST click (initial focus)
                      // After that, allow free cursor movement
                      if (!hasFocusedInput && textareaRef.current) {
                        const length = textareaRef.current.value.length;
                        textareaRef.current.setSelectionRange(length, length);
                        setHasFocusedInput(true);
                      }
                      // Subsequent clicks: let user position cursor freely (default browser behavior)
                    }}
                    className={`w-full px-4 py-2 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border-2 ${themeConfig.inputBorder} rounded-lg ${themeConfig.inputText} ${theme === 'light' ? 'placeholder-gray-400' : 'placeholder-zinc-400'} focus:outline-none min-h-[150px] mb-4`}
                    placeholder={editingItem.field === 'question' ? t('enter_question') : t('enter_reply')}
                  />
                  <button
                    onClick={handleSaveEdit}
                    className="bg-jade-purple-dark text-white hover:bg-jade-purple hover:shadow-md hover:bg-jade-purple transition-all duration-200 px-6 py-2 rounded-lg font-medium w-full border border-jade-purple/75"
                  >
                    {t('done')}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Update Confirmation Modal */}
          {showConfirmation && (
            <div
              className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
            >
              <div
                ref={confirmModalRef}
                className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-jade-purple/[0.8] border-jade-purple/50'}`}
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                {theme === 'dark' && (
                  <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/10 to-transparent opacity-50 rounded-2xl"></div>
                )}
                <div className="relative z-10">
                  <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                    {t('change_knowledge_base')}
                  </h3>
                  <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                    {t('change_question_confirmation')}
                  </p>
                  <div className="flex justify-between w-full space-x-4">
                    <Button
                      onClick={() => setShowConfirmation(false)}
                      variant="secondary"
                      size="md"
                      className="flex-1"
                    >
                      {t('cancel')}
                    </Button>
                    <Button
                      onClick={saveQAToSupabase}
                      variant="primary"
                      size="md"
                      className="flex-1"
                    >
                      {t('confirm')}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Cancel Confirmation Modal */}
          {showCancelConfirmation && (
            <div
              className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
            >
              <div
                ref={cancelConfirmModalRef}
                className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                {theme === 'dark' && (
                  <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
                )}
                <div className="relative z-10">
                  <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                    {t('kb_discard_changes')}
                  </h3>
                  <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                    {t('kb_unsaved_changes')}
                  </p>
                  <div className="flex justify-between w-full space-x-4">
                    <Button
                      onClick={() => setShowCancelConfirmation(false)}
                      variant="secondary"
                      size="md"
                      className="flex-1"
                    >
                      {t('kb_keep_editing')}
                    </Button>
                    <Button
                      onClick={confirmCancel}
                      variant="danger"
                      size="md"
                      className="flex-1"
                    >
                      {t('kb_discard')}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Delete Confirmation Modal */}
          {deleteConfirm && (
            <div
              className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
            >
              <div
                ref={deleteModalRef}
                className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                {theme === 'dark' && (
                  <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
                )}
                <div className="relative z-10">
                  <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                    {t('delete_item')}
                  </h3>
                  <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                    {t('kb_delete_confirmation')}
                  </p>
                  <div className="flex justify-between w-full space-x-4">
                    <Button
                      onClick={() => setDeleteConfirm(null)}
                      variant="secondary"
                      size="md"
                      className="flex-1"
                      disabled={isDeleting}
                    >
                      {t('cancel')}
                    </Button>
                    <Button
                      onClick={handleDeleteFromDatabase}
                      variant="danger"
                      size="md"
                      className="flex-1"
                      disabled={isDeleting}
                    >
                      {isDeleting ? (
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        t('delete')
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Update Status Overlay */}
          {updateStatus !== 'idle' && (
            <div
              className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50"
              onClick={() => updateStatus !== 'loading' && setUpdateStatus('idle')}
            >
              <div
                className={`relative ${
                  updateStatus === 'loading' 
                    ? 'bg-jade-purple-dark/[0.6] border-white/20' 
                    : updateStatus === 'success'
                      ? 'bg-green-900/[0.8] border-green-500/50'
                      : 'bg-red-900/[0.8] border-red-500/50'
                } backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden`}
                onClick={(e) => e.stopPropagation()}
              >
                <div className={`absolute inset-0 bg-gradient-to-br ${
                  updateStatus === 'loading'
                    ? 'from-jade-purple/10'
                    : updateStatus === 'success'
                      ? 'from-green-500/10'
                      : 'from-red-500/10'
                } to-transparent opacity-50 rounded-2xl`}></div>
                <div className="relative z-10">
                  {updateStatus === 'loading' ? (
                    <div className="flex flex-col items-center text-center">
                      <div className="w-16 h-16 mb-4 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                      <p className="text-lg font-semibold mb-3 text-white">{t('changing_knowledge_base')}</p>
                      <div className="w-full bg-black/30 rounded-full h-3 mb-1">
                        <div
                          className="bg-white h-3 rounded-full transition-all duration-300"
                          style={{ width: `${updateProgress}%` }}
                        ></div>
                      </div>
                      <p className="text-sm text-white/80">{updateProgress}% {t('complete')}</p>
                      <p className="text-sm mt-2 text-white/80">{updateMessage}</p>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <div className={`w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
                        updateStatus === 'success' ? 'bg-green-900/[0.8]' : 'bg-red-900/[0.8]'
                      }`}>
                        {updateStatus === 'success' ? (
                          <svg className="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        ) : (
                          <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        )}
                      </div>
                      <p className="text-lg font-semibold mb-1 text-white">
                        {updateStatus === 'success' ? t('success') : t('error')}
                      </p>
                      <p className="text-white/80 text-center text-sm">{updateMessage}</p>
                      <Button
                        onClick={() => setUpdateStatus('idle')}
                        variant={updateStatus === 'success' ? 'success' : 'danger'}
                        size="sm"
                        className="mt-4"
                      >
                        {t('close')}
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Recently Added Section - Only show when recentQA is set */}
          {recentQA && (
            <div
              className={`relative backdrop-blur-xl rounded-2xl p-6 mb-8 border transition-all duration-300 group overflow-visible ${theme === 'light' ? 'bg-white border-gray-300 hover:border-gray-400' : 'bg-white/[0.075] border-white/20 hover:border-white/40'}`}
              ref={updateSectionRef}
            >

              <div className="relative z-10">
              <div className="flex flex-row justify-between items-center mb-6">
                <div className="flex-1">
                  <h2 className={`text-xl font-bold mb-1 font-title ${themeConfig.textPrimary}`}>{t('change_button')}</h2>
                  <p className={`${themeConfig.textSecondary} text-sm font-body`}>
                    {t('change_business_insights')}
                  </p>
                </div>
                <div>
                  <Button
                    onClick={handleUpdate}
                    variant="primary"
                    size="md"
                    disabled={isUpdating || !recentQA || !hasChanges() || !hasValidAnswer()}
                    className={!hasChanges() || !hasValidAnswer() || isUpdating || !recentQA ? 'opacity-60' : ''}
                  >
                    {isUpdating ? t('changing') : t('change_button')}
                  </Button>
                </div>
              </div>

              {/* List Items - Moved product search and selection to top */}
              <div className="w-full">
                <div className="space-y-4">

                  {/* Photo Search Bar */}
                  <div className="mb-4 relative">
                    <div className="relative">
                      <input
                        type="text"
                        value={photoSearchQuery}
                        onChange={handlePhotoSearch}
                        onFocus={() => {
                          if (photoSearchResults.length > 0) {
                            setShowPhotoResults(true);
                          } else if (photoSearchQuery) {
                            // If there's a query but no results, trigger a new search
                            searchPhotos(photoSearchQuery);
                          }
                        }}
                        placeholder={t('kb_search_photo')}
                        className={`w-full px-3 py-2 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} rounded-lg ${themeConfig.inputText} focus:outline-none focus:border-gray-500 hover:border-gray-500`}
                        style={{
                          fontSize: '16px' // Prevent auto-zoom on mobile
                        }}
                        autoComplete="off" // Prevent browser autocomplete from interfering
                        spellCheck="false" // Disable spell checking
                      />
                      {isSearching && (
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                        </div>
                      )}
                    </div>

                    {/* Search Results Dropdown */}
                    {showPhotoResults && (
                      <div
                        className={`absolute z-50 w-full mt-2 ${themeConfig.cardBackground} ${themeConfig.cardBorder} rounded-xl max-h-60 overflow-y-auto shadow-lg`}
                        ref={searchResultsRef}
                      >
                        {photoSearchResults.length > 0 ? (
                          photoSearchResults.map((photo, index) => (
                            <div
                              key={`${photo.id}-${photo.photo_id}-${index}`}
                              className={`flex items-center gap-3 p-3 cursor-pointer border-b ${themeConfig.cardBorder} last:border-0 transition-colors duration-200 ${theme === 'light' ? 'hover:bg-gray-200' : 'hover:bg-white/10'}`}
                              onClick={() => handleSelectPhoto(photo)}
                              style={{
                                transition: 'all 0.2s ease'
                              }}
                            >
                              {/* Photo Thumbnail - Optimized */}
                              <PhotoThumbnail
                                photo={photo}
                                className="w-10 h-10"
                              />
                              {/* Photo ID */}
                              <div className="flex-1 truncate">
                                <p className={`${themeConfig.textPrimary} truncate`}>{photo.photo_id}</p>
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className={`p-3 ${themeConfig.textMuted} text-center`}>
                            {t('no_photos_found')}
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Selected Photo Display or Loading Animation */}
                  {isPhotoLoading ? (
                    <div className={`mb-4 p-4 ${themeConfig.cardBackground} border ${themeConfig.cardBorder} rounded-xl flex items-center justify-center h-16`}>
                      <div className="flex items-center space-x-3">
                        <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                        <span className={`${themeConfig.textMuted} text-sm`}>{t('loading')}</span>
                      </div>
                    </div>
                  ) : selectedProduct && (
                    <div className={`mb-4 p-3 border ${themeConfig.cardBorder} hover:border-gray-500 rounded-xl flex items-center justify-between animate-fadeIn transition-colors duration-200`}>
                      <div className="flex items-center gap-3">
                        {/* Photo Thumbnail */}
                        <div className={`w-10 h-10 ${theme === 'light' ? 'bg-gray-100' : 'bg-black/50'} rounded overflow-hidden flex-shrink-0 border ${themeConfig.cardBorder}`}>
                          {selectedProduct.photo_url ? (
                            <img
                              src={selectedProduct.photo_url}
                              alt={selectedProduct.name}
                              className="w-full h-full object-cover cursor-pointer"
                              onClick={() => handleViewImage(selectedProduct.full_photo_urls)}
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = '/placeholder-image.jpg';
                              }}
                            />
                          ) : (
                            <div className={`w-full h-full flex items-center justify-center ${theme === 'light' ? 'bg-gray-50' : 'bg-white/5'} ${themeConfig.textMuted}`}>
                              <span>{t('kb_no_photo')}</span>
                            </div>
                          )}
                        </div>
                        {/* Photo ID */}
                        <div>
                          <p className={themeConfig.textPrimary}>{selectedProduct.name}</p>
                        </div>
                      </div>
                      {/* Remove Button */}
                      <button
                        onClick={handleClearSelectedProduct}
                        className={`p-1 rounded-full ${theme === 'light' ? 'bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800' : 'bg-white/5 hover:bg-white/10 text-zinc-300 hover:text-white'} border ${themeConfig.cardBorder} transition-colors duration-200`}
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  )}

                  {/* Question Field (View-only with modal) */}
                  <div className={`border ${themeConfig.cardBorder} rounded-lg p-4 mb-4`}>
                    <div className={`font-medium ${themeConfig.textSecondary} mb-2`}>{t('question')} <span className={`text-sm ${themeConfig.textMuted}`}>({t('kb_question_cannot_be_edited')})</span></div>
                    <div
                      className={`w-full px-3 py-2 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} rounded-lg ${themeConfig.textPrimary} ${theme === 'light' ? 'hover:bg-gray-50' : 'hover:bg-black/40'} hover:border-gray-500 transition-colors cursor-pointer`}
                      onClick={() => handleViewItem('question', recentQA.question)}
                      title={t('click_to_view')}
                    >
                      <div className="truncate break-words">
                        {recentQA.question}
                      </div>
                    </div>
                  </div>

                  {/* Answer Edit Field */}
                  <div className={`border ${themeConfig.cardBorder} rounded-lg p-4`}>
                    <div className={`font-medium ${themeConfig.textSecondary} mb-2`}>{t('reply')}</div>
                    <div className="w-full relative">
                      <div
                        className={`w-full px-3 py-2 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} rounded-lg ${themeConfig.textPrimary} ${!recentQA.audio_url && !recentQA.localAudio ? (theme === 'light' ? 'hover:bg-gray-50 cursor-pointer' : 'hover:bg-black/40 cursor-pointer') : ''} hover:border-gray-500 transition-all`}

                        onClick={() => !recentQA.audio_url && !recentQA.localAudio && handleStartEdit(recentQA.id, 'answer', recentQA.answer)}
                      >
                        <div className={`${!recentQA.answer && !recentQA.audio_url && !recentQA.localAudio ? themeConfig.textMuted : ''} truncate break-words pr-10`}
                             title={!recentQA.audio_url && !recentQA.localAudio ? t('click_to_edit') : undefined}>
                          {/* Only show text answer if no audio exists */}
                          {!recentQA.audio_url && !recentQA.localAudio ?
                            (recentQA.answer || t('enter_reply')) :
                            ""}
                          {recentQA.localAudio && (
                            <div className={`mt-1 ${themeConfig.textPrimary} flex items-center justify-between`}>
                              <div className="flex items-center">
                                <button
                                  className="mr-3 hover:opacity-80 focus:outline-none"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    // First try using newlyRecordedAudio if it matches this QA item since it's freshest
                                    if (newlyRecordedAudio?.id === recentQA.id) {
                                      handleNewAudioPlayback(recentQA.id, newlyRecordedAudio.url, newlyRecordedAudio.duration);
                                    }
                                    // Fall back to localAudio if available
                                    else if (recentQA.localAudio) {
                                      handleNewAudioPlayback(recentQA.id, recentQA.localAudio.url, recentQA.localAudio.duration);
                                    }
                                  }}
                                  title={audioPlayback?.id === recentQA.id && audioPlayback?.type === 'new' ? t('kb_pause_audio') : t('kb_play_audio')}
                                >
                                  {audioPlayback?.id === recentQA.id && audioPlayback?.type === 'new' ? (
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                  ) : (
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                  )}
                                </button>
                                <span className="text-sm">
                                  {t('audio')}: {audioPlayback?.id === recentQA.id && audioPlayback?.type === 'new' && audioPlayback?.remainingTime !== undefined
                                    ? `${audioPlayback?.remainingTime}s` // Playing - show countdown
                                    : newlyRecordedAudio?.id === recentQA.id
                                      ? `${newlyRecordedAudio.duration}s` // Not playing, use newlyRecordedAudio duration
                                      : recentQA.localAudio?.duration && recentQA.localAudio.duration > 0
                                        ? `${recentQA.localAudio.duration}s` // Use localAudio duration if available
                                        : '1s' // Fallback to 1s if all else fails
                                  }
                                </span>
                              </div>
                              <button
                                className="text-red-500 hover:text-red-700 focus:outline-none absolute right-3"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleConfirmAudioDelete(recentQA.id);
                                }}
                                title={t('kb_delete_audio')}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          )}
                          {recentQA.audio_url && (
                            <div className={`mt-1 ${themeConfig.textPrimary} flex items-center justify-between`}>
                              <div className="flex items-center">
                                <button
                                  className="mr-3 hover:opacity-80 focus:outline-none"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleAudioPlayback(recentQA.id, recentQA.audio_url, recentQA.audio_duration);
                                  }}
                                  title={audioPlayback?.id === recentQA.id && audioPlayback?.type === 'existing' ? t('kb_pause_audio') : t('kb_play_audio')}
                                >
                                  {audioPlayback?.id === recentQA.id && audioPlayback?.type === 'existing' ? (
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                  ) : (
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                  )}
                                </button>
                                <span className="text-sm">
                                  {t('audio')}: {audioPlayback?.id === recentQA.id && audioPlayback?.type === 'existing' && audioPlayback?.remainingTime !== undefined
                                    ? `${audioPlayback?.remainingTime}s`
                                    : `${recentQA.audio_duration || '0'}s`}
                                </span>
                              </div>
                              <button
                                className="text-red-500 hover:text-red-700 focus:outline-none absolute right-3"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleConfirmAudioDelete(recentQA.id);
                                }}
                                title={t('kb_delete_audio')}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Add microphone button when no audio is present */}
                      {!recentQA.audio_url && !recentQA.localAudio && (
                        <button
                          className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-1.5 rounded-full transition-colors border ${
                            isCheckingPermission && recordingItem === recentQA.id
                              ? 'bg-jade-purple/50 border-jade-purple animate-pulse cursor-not-allowed'
                              : 'bg-black/30 hover:bg-jade-purple border-white/20 hover:border-jade-purple-dark'
                          }`}
                          onClick={() => startRecordingForItem(recentQA.id)}
                          disabled={isCheckingPermission && recordingItem === recentQA.id}
                          title={t('kb_record_audio_answer')}
                        >
                          {isCheckingPermission && recordingItem === recentQA.id ? (
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                            </svg>
                          )}
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Cancel/Close Button */}
                  <div className="flex justify-end">
                    <Button
                      variant="danger"
                      size="md"
                      onClick={handleDelete}
                    >
                      {t('cancel')}
                    </Button>
                  </div>
                </div>
                </div>
              </div>
            </div>
          )}

          {/* Knowledge Base Section */}
          <div
            ref={(el) => {
              if (knowledgeBaseRef.current !== el) {
                (knowledgeBaseRef as any).current = el;
              }
              if (databaseSectionRef.current !== el) {
                (databaseSectionRef as any).current = el;
              }
            }}
            className={`relative ${themeConfig.cardBackground} rounded-2xl p-6 mb-8 border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} transition-all duration-300 group overflow-hidden`}
          >

            <div className="relative z-10">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
              <div>
                <h2 className={`text-xl font-bold mb-1 font-title ${themeConfig.textPrimary}`}>{t('kb_title')}</h2>
                <p className={`${themeConfig.textSecondary} text-sm font-body`}>
                  {t('kb_subtitle')}
                </p>
              </div>
              {/* Only show search when data is loaded */}
              {!isLoading && (
                <div className="mt-4 md:mt-0 w-full md:w-auto">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder={t('kb_search_questions')}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className={`w-full md:w-64 px-4 py-2 pl-10 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} rounded-lg ${themeConfig.inputText} focus:outline-none focus:border-gray-500 hover:border-gray-500`}
                    />
                    <svg className={`w-5 h-5 ${themeConfig.textMuted} absolute left-3 top-2.5`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              )}
            </div>

            {/* Loading State */}
            {isLoading && (
              <>
                {/* Loading header */}
                <div className="py-8 flex justify-center items-center">
                  <div className="flex flex-col items-center space-y-4">
                    {/* Main loading spinner */}
                    <div className="relative">
                      <div className="w-12 h-12 border-4 border-jade-purple/20 border-t-jade-purple rounded-full animate-spin"></div>
                      <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-jade-purple/40 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
                    </div>

                    {/* Loading text with animation */}
                    <div className="text-center">
                      <p className="text-white text-lg font-medium mb-1">{t('kb_loading_questions')}</p>
                      {/* <p className="text-zinc-400 text-sm">
                        {questions.length > 0
                          ? `${t('kb_loaded')} ${questions.length} ${t('kb_questions')}...`
                          : t('kb_preparing_knowledge_base')
                        }
                      </p> */}
                    </div>

                    {/* Progress indicator */}
                    <div className="w-48 h-1 bg-zinc-700 rounded-full overflow-hidden">
                      <div className="h-full bg-gradient-to-r from-jade-purple to-jade-purple-dark rounded-full animate-pulse"></div>
                    </div>
                  </div>
                </div>

                {/* Skeleton loading for FAQ list */}
                <div className="space-y-3">
                  {/* Column headers skeleton */}
                  <div className="flex border-b border-zinc-800 py-3 font-semibold text-zinc-400 font-body">
                    <div className="w-[6%] px-2"></div>
                    <div className="w-[35%] px-2">
                      <div className="h-4 bg-zinc-700/50 rounded animate-pulse"></div>
                    </div>
                    <div className="w-[35%] px-2">
                      <div className="h-4 bg-zinc-700/50 rounded animate-pulse"></div>
                    </div>
                    <div className="w-[15%] px-2">
                      <div className="h-4 bg-zinc-700/50 rounded animate-pulse"></div>
                    </div>
                    <div className="w-[9%] px-2"></div>
                  </div>

                  {/* Skeleton FAQ rows */}
                  {Array.from({ length: 8 }).map((_, index) => (
                    <div key={index} className="flex border-b border-zinc-800/50 py-3 items-center">
                      <div className="w-[6%] px-2">
                        <div className="h-4 w-6 bg-zinc-700/30 rounded animate-pulse"></div>
                      </div>
                      <div className="w-[35%] px-2">
                        <div className="h-4 bg-zinc-700/30 rounded animate-pulse" style={{ animationDelay: `${index * 0.1}s` }}></div>
                      </div>
                      <div className="w-[35%] px-2">
                        <div className="h-4 bg-zinc-700/30 rounded animate-pulse" style={{ animationDelay: `${index * 0.15}s` }}></div>
                      </div>
                      <div className="w-[15%] px-2">
                        <div className="h-8 w-8 bg-zinc-700/30 rounded animate-pulse" style={{ animationDelay: `${index * 0.2}s` }}></div>
                      </div>
                      <div className="w-[9%] px-2 flex flex-col space-y-1">
                        <div className="h-6 w-6 bg-zinc-700/30 rounded animate-pulse" style={{ animationDelay: `${index * 0.25}s` }}></div>
                        <div className="h-6 w-6 bg-zinc-700/30 rounded animate-pulse" style={{ animationDelay: `${index * 0.3}s` }}></div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}

            {/* Error State */}
            {error && !isLoading && (
              <div className="py-12 text-center text-red-400">
                <p>{error}</p>
                <button
                  onClick={fetchQuestions}
                  className="mt-4 px-4 py-2 bg-zinc-800 hover:bg-zinc-700 rounded-lg text-white"
                >
                  {t('kb_try_again')}
                </button>
              </div>
            )}

            {/* Empty State */}
            {!isLoading && !error && filteredQuestions.length === 0 && (
              <div className={`py-12 text-center ${themeConfig.textMuted}`}>
                {searchQuery ? (
                  <p>{t('kb_no_questions_found')}</p>
                ) : (
                  <p>{t('kb_no_questions_added')}</p>
                )}
              </div>
            )}

            {/* Knowledge Base Content */}
            {!isLoading && !error && filteredQuestions.length > 0 && (
              <>
                {/* Knowledge Base Column Headers */}
                <div className={`flex border-b ${theme === 'light' ? 'border-gray-200' : 'border-zinc-800'} py-3 font-semibold ${themeConfig.textSecondary} font-body`}>
                  <div className="w-[6%] text-left"></div>
                  <div className="w-[35%] text-left">{t('kb_question_column')}</div>
                  <div className="w-[35%] text-left">{t('kb_reply_column')}</div>
                  <div className="w-[15%] text-left"></div>
                  <div className="w-[9%] text-center"></div>
                </div>

                {/* Virtual Scrolling Container */}
                <div
                  ref={scrollContainerRef}
                  className="w-full relative"
                >
                  {/* Virtual list container with total height */}
                  <div style={{ height: getTotalHeight(), position: 'relative' }}>
                    {/* Render only visible items */}
                    {filteredQuestions.slice(visibleStartIndex, visibleEndIndex + 1).map((qa, index) => {
                      const actualIndex = visibleStartIndex + index;
                      return (
                        <div
                          key={qa.id}
                          className={`flex border-b ${theme === 'light' ? 'border-gray-200' : 'border-zinc-800'} py-3 items-center absolute w-full`}
                          style={getItemStyle(actualIndex)}
                        >
                          <div className={`w-[6%] text-left ${themeConfig.textMuted} font-body`}>
                            {actualIndex + 1}
                          </div>
                      <div
                        className={`w-[35%] cursor-pointer rounded py-1 transition-colors`  }
                        onClick={() => handleViewItem('question', qa.question)}
                      >
                        <div className={`truncate break-words ${themeConfig.textPrimary}`} title={qa.question}>
                          {qa.question}
                        </div>
                      </div>
                      <div
                        className={`w-[35%] py-1 rounded transition-colors cursor-pointer`  }
                        onClick={() => handleViewItem('answer', qa.answer)}
                      >
                        <div className={`truncate break-words ${themeConfig.textPrimary}`} title={qa.answer}>
                          {/* Conditional Rendering for Answer/Audio */}
                          {qa.localAudio ? (
                            // Display New Audio Playback UI
                            <div className={`mt-1 ${themeConfig.textPrimary} flex items-center justify-between pr-1`}>
                              <div className="flex items-center">
                                 <button
                                   className="mr-3 hover:opacity-80 focus:outline-none"
                                   onClick={(e) => {
                                     e.stopPropagation(); // Prevent triggering other clicks
                                     if (qa.localAudio) { // Type guard
                                         handleNewAudioPlayback(qa.id, qa.localAudio.url, qa.localAudio.duration);
                                     }
                                   }}
                                   title={audioPlayback?.id === qa.id && audioPlayback?.type === 'new' ? t('kb_pause_audio') : t('kb_play_recorded_audio')}
                                 >
                                   {/* Play/Pause Icon based on unified audioPlayback state */}
                                   {audioPlayback?.id === qa.id && audioPlayback?.type === 'new' ? (
                                     <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                     </svg>
                                   ) : (
                                     <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                     </svg>
                                   )}
                                 </button>
                                 <span className="text-sm">
                                   {t('audio')}: {audioPlayback?.id === qa.id && audioPlayback?.type === 'new' && audioPlayback?.remainingTime !== undefined
                                     ? `${audioPlayback?.remainingTime}s`
                                     : newlyRecordedAudio?.id === qa.id
                                       ? `${newlyRecordedAudio.duration}s`
                                       : qa.localAudio?.duration && qa.localAudio.duration > 0
                                         ? `${qa.localAudio.duration}s`
                                         : '1s'
                                   }
                                 </span>
                              </div>
                              {/* Optionally add a delete/re-record button here if needed */}
                            </div>
                          ) : qa.audio_url ? (
                            // Display Existing Audio Playback UI (unchanged)
                            // Don't show text answer when audio exists
                            <div className={`mt-1 ${themeConfig.textPrimary} flex items-center justify-between pr-1`}>
                              {/* ... existing audio playback button and time ... */}
                              <div className="flex items-center">
                                  <button
                                    className="mr-3 hover:opacity-80 focus:outline-none"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleAudioPlayback(qa.id, qa.audio_url, qa.audio_duration);
                                    }}
                                    title={audioPlayback?.id === qa.id && audioPlayback?.type === 'existing' ? t('kb_pause_audio') : t('kb_play_audio')}
                                  >
                                    {audioPlayback?.id === qa.id && audioPlayback?.type === 'existing' ? (
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                      </svg>
                                    ) : (
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                      </svg>
                                    )}
                                  </button>
                                  <span className="text-sm">
                                     {audioPlayback?.id === qa.id && audioPlayback?.type === 'existing' && audioPlayback?.remainingTime !== undefined
                                      ? `(${audioPlayback?.remainingTime}s)`
                                      : `(${qa.audio_duration || '0'}s)`}
                                  </span>
                                </div>
                            </div>
                          ) : (
                             // Display Text Answer only
                             qa.answer
                          )}
                        </div>
                      </div>
                      <div className="w-[15%]">
                        {qa.photo_url && (
                          <div className="h-full w-full px-3 py-1.5 rounded-lg flex flex-col items-center justify-center">
                            <PhotoThumbnail
                              photo={{
                                photo_url: extractImageUrls(qa.photo_url).allUrls || [],
                                photo_id: qa.photo_id || 'FAQ Image'
                              }}
                              className="w-10 h-10 border border-white/20"
                              onClick={() => handleViewImage(qa.photo_url)}
                            />
                          </div>
                        )}
                      </div>
                      <div className="w-[9%] flex flex-col space-y-1">
                        <button
                          className="text-jade-purple hover:text-jade-purple-dark text-lg p-3"
                          onClick={() => handleMoveToRecentlyAdded(qa)}
                          title={t('kb_edit_question')}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          className="text-red-500 hover:text-red-400 text-lg p-3 transition-colors"
                          onClick={() => showDeleteConfirmation(qa.id, !!qa.audio_url, qa.audio_file_path)}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  )
                })}
                  </div>
                </div>
              </>
            )}
            </div>
          </div>
        </motion.div>
      </div>

      <Footer />

      {/* Audio Delete Confirmation Modal */}
      {confirmAudioDelete !== null && (
        <div
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
        >
          <div
            ref={audioDeleteModalRef}
            className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            {theme === 'dark' && (
              <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
            )}
            <div className="relative z-10">
              <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                {t('delete_audio')}
              </h3>
              <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                {t('kb_delete_audio_message')}
              </p>
              <div className="flex justify-between w-full space-x-4">
                <Button
                  onClick={() => setConfirmAudioDelete(null)}
                  variant="secondary"
                  size="md"
                  className="flex-1"
                >
                  {t('cancel')}
                </Button>
                <Button
                  onClick={handleDeleteAudio}
                  variant="danger"
                  size="md"
                  className="flex-1"
                >
                  {t('delete')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Audio Recording Modal - Updated */}
      {isRecordingAudio && (
        <div
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
        >
          <div
            ref={audioRecordModalRef}
            className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-jade-purple-dark/[0.3] border-white/20'}`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            <div className="relative z-10">
              {/* Close button (X) - always visible but behavior changes when recording */}
              <button
                className={`absolute top-0 right-0 p-1 rounded-full ${theme === 'light' ? 'bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 border-gray-300' : 'bg-black/40 hover:bg-jade-purple text-white/60 hover:text-white border-white/20'} border transition-colors`}
                onClick={isRecording ? stopRecording : cancelRecording}
                aria-label="Close"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>

              <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                {isProcessingAudio ? t('kb_processing_audio') : t('kb_record_audio')}
              </h3>

              <div className="flex justify-center items-center mb-6 h-32">
                {isProcessingAudio ? (
                  // Show only spinner when processing
                  <div className="w-16 h-16 border-4 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  // Show recording indicator circle otherwise
                  <div className={`relative w-32 h-32 rounded-full flex items-center justify-center ${
                    isRecording ? 'bg-red-500/20' : (theme === 'light' ? 'bg-gray-100' : 'bg-black/30')
                  } border-2 ${isRecording ? 'border-red-500' : (theme === 'light' ? 'border-gray-300' : 'border-white/20')}`}>
                    <div className="text-center">
                      <div className={`text-2xl font-mono ${themeConfig.textPrimary}`}>
                        {`${recordingDuration}s`}
                      </div>
                      <div className={`text-sm ${themeConfig.textMuted} mt-1`}>
                        {isRecording ? (
                          <span className="inline-flex items-center">
                            <span className="inline-block w-2 h-2 bg-red-500 rounded-full mr-1.5 animate-pulse"></span>
                            {t('kb_recording')}
                          </span>
                        ) : (
                          t('kb_ready')
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Buttons only shown when not processing */}
              {!isProcessingAudio && (
                <div className="flex justify-center gap-4">
                  {!isRecording ? (
                    <button
                      onClick={startRecording}
                      disabled={isCheckingPermission}
                      className="py-2.5 px-6 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg flex items-center justify-center border-2 border-jade-purple/80 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isCheckingPermission ? (
                        <>
                          <div className="w-5 h-5 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          {audioPermissionState === 'requesting' ? 'Requesting Permission...' : 'Preparing...'}
                        </>
                      ) : (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                          </svg>
                          {audioPermissionState === 'granted' ? t('start_recording') : 'Ready to Record'}
                        </>
                      )}
                    </button>
                  ) : (
                    <button
                      onClick={stopRecording}
                      className="py-2.5 px-6 bg-red-700/90 hover:bg-red-700 text-white rounded-lg flex items-center justify-center border border-red-600 transition-all duration-200"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                      </svg>
                      {t('stop_recording')}
                    </button>
                  )}
                </div>
            )}
            </div>
          </div>
        </div>
      )}

      {/* Cancel Confirmation Modal */}
      {showCancelConfirmation && (
        <div
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
        >
          <div
            ref={cancelConfirmModalRef}
            className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            {theme === 'dark' && (
              <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
            )}
            <div className="relative z-10">
              <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                {t('kb_discard_changes')}
              </h3>
              <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                {t('kb_unsaved_changes')}
              </p>
              <div className="flex justify-between w-full space-x-4">
                <Button
                  onClick={() => setShowCancelConfirmation(false)}
                  variant="secondary"
                  size="md"
                  className="flex-1"
                >
                  {t('kb_keep_editing')}
                </Button>
                <Button
                  onClick={confirmCancel}
                  variant="danger"
                  size="md"
                  className="flex-1"
                >
                  {t('kb_discard')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Image Gallery Modal */}
      {imageGallery && (
        <div className="fixed inset-0 bg-black bg-opacity-20 backdrop-blur-lg flex items-center justify-center z-50">
          <div
            ref={imageGalleryRef}
            className={`relative ${themeConfig.cardBackground} rounded-2xl p-6 w-full max-w-3xl mx-4 border ${themeConfig.cardBorder} overflow-hidden`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            <div className="relative z-10">
              {/* Close button in the top-right corner */}
              <button
                onClick={() => setImageGallery(null)}
                className="absolute top-0 right-0 p-1.5 border border-white/20 rounded-full bg-zinc-800 hover:bg-jade-purple text-zinc-400 hover:text-white transition-colors z-20"
                aria-label="Close"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>

              <div className="flex flex-col">
                {/* Image counter */}
                <div className="text-center mb-2 text-sm text-zinc-400">
                  {imageGallery.currentIndex + 1} / {imageGallery.urls.length}
                </div>

                {/* Main image container with touch events */}
                <div
                  className="relative h-[60vh] flex items-center justify-center"
                  onTouchStart={handleTouchStart}
                  onTouchMove={handleTouchMove}
                  onTouchEnd={handleTouchEnd}
                  aria-live="polite"
                  role="region"
                  aria-label={`Image ${imageGallery.currentIndex + 1} of ${imageGallery.urls.length}`}
                >
                  {imageGallery.urls.length === 0 ? (
                    <div className="text-center text-zinc-400 p-8 bg-zinc-800/50 rounded-lg">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 text-zinc-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p>{t('no_images_available')}</p>
                    </div>
                  ) : (
                    <div className="relative">
                      <img
                        src={optimizeGalleryImage(imageGallery.urls[imageGallery.currentIndex])}
                        alt={`Image ${imageGallery.currentIndex + 1} of ${imageGallery.urls.length}`}
                        className="rounded-lg max-w-full max-h-full object-contain"
                        loading="eager"
                        decoding="async"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/placeholder-image.svg';
                        }}
                      />
                    </div>
                  )}

                  {/* Navigation buttons - only show if more than one image */}
                  {imageGallery.urls.length > 1 && (
                    <>
                      {/* Previous button */}
                      <button
                        onClick={showPreviousImage}
                        className="absolute left-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                        aria-label="Previous image"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                      </button>

                      {/* Next button */}
                      <button
                        onClick={showNextImage}
                        className="absolute right-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                        aria-label="Next image"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </button>
                    </>
                  )}
                </div>

                {/* Thumbnail strip - only show if more than one image */}
                {imageGallery.urls.length > 1 && (
                  <div className="flex justify-center space-x-2 mt-4 overflow-x-auto py-2">
                    {imageGallery.urls.map((url, index) => (
                      <PhotoThumbnail
                        key={index}
                        photo={{
                          photo_url: [url],
                          photo_id: `thumbnail-${index + 1}`
                        }}
                        className={`w-16 h-16 rounded-lg border-2 transition-all duration-200 ease-in-out ${
                          index === imageGallery.currentIndex ? 'border-white/80 scale-105 opacity-100' : 'border-zinc-700 opacity-70 hover:opacity-90 hover:border-zinc-600'
                        }`}
                        onClick={() => {
                          setImageGallery({ ...imageGallery, currentIndex: index });
                        }}
                      />
                    ))}
                </div>
              )}
            </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
