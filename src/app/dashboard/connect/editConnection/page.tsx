'use client'

import Link from 'next/link'
import Image from 'next/image'
import { useState, useEffect } from 'react'
import { useAuth } from '@/context/AuthContext'
import { useDashboardData } from '@/hooks/useOptimizedData'
import { FaFacebookMessenger, FaInstagram, FaPaperPlane, FaGlobe } from 'react-icons/fa'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { Button, LinkButton } from '@/components/ui'

// Define platform type for documentation purposes
// 'facebook' | 'instagram' | 'whatsapp' | 'telegram' | 'web' | 'tiktok';

// Define sanitized client credentials type (from secure API)
type ClientCredentials = {
  id: number;
  client_id: string;
  fb_name?: string;
  ig_name?: string;
  tg_name?: string;
  wa_name?: string;
  web_name?: string;
  web_domain?: string;
  fb_status?: number;
  ig_status?: number;
  wa_status?: number;
  tg_status?: number;
  web_status?: number;
  tg_biz_id?: string;
  // Security: URLs and tokens are not exposed, only boolean flags
  has_fb_token: boolean;
  has_ig_token: boolean;
  has_wa_token: boolean;
  has_tg_token: boolean;
  has_tg_biz: boolean;
  has_web_domain: boolean;
};

export default function EditConnectionPage() {
  // const { user: _ } = useAuth() // Unused but kept for context
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Use dashboard cache for client info
  const { data: dashboardData } = useDashboardData()
  const clientInfo = dashboardData?.clientInfo

  // State variables
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [isClient, setIsClient] = useState(false)
  const [credentials, setCredentials] = useState<ClientCredentials | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  // Connection limit is fetched but not used in edit page since we're only editing existing connections

  // State for delete confirmation
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState<{show: boolean, platform: string | null}>({
    show: false,
    platform: null
  })

  // State for tracking if deletion is in progress
  const [isDeleting, setIsDeleting] = useState(false)

  // Track connected platforms
  const [connectedPlatforms, setConnectedPlatforms] = useState<{[key: string]: boolean}>({
    facebook: false,
    instagram: false,
    /* whatsapp: false, */
    telegram: false,
    web: false
  })

  // Note: Removed token values and input refs since this page only shows connected platforms for disconnection

  // Removed deleteConfirmModalRef - now using onClick on backdrop for better performance

  // Set isClient to true when component mounts
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Disable page scroll when any popup is open
  useEffect(() => {
    if (errorMessage || successMessage || showDeleteConfirmation.show) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [errorMessage, successMessage, showDeleteConfirmation.show])

  // Wait for dashboard data to load before fetching other data
  useEffect(() => {
    if (clientInfo?.client_id) {
      fetchClientCredentials();
    }
  }, [clientInfo?.client_id]) // eslint-disable-line react-hooks/exhaustive-deps

  // Removed useEffect for click outside - now using onClick on backdrop for better performance

  // Note: Removed fetchSubscriptionLimits since connection limits are not needed for disconnection

  // Fetch disconnect data via targeted API (only necessary fields)
  const fetchClientCredentials = async () => {
    try {
      setIsLoading(true)

      // Wait for dashboard data to load before fetching credentials
      if (!clientInfo?.client_id) {
        setIsLoading(false)
        return
      }

      /*
      === FOR EXTERNAL WORKFLOW SETUP REFERENCE ===
      This API call shows what data the external workflow should return for connected platforms.

      External workflow should query database client_credentials table and return:
      {
        "credentials": {
          "id": 123,
          "client_id": "client_abc",
          "fb_name": "Page Name",
          "ig_name": "Instagram Name",
          "tg_name": "Bot Name",
          "web_domain": "example.com",
          "fb_status": 1,
          "ig_status": 1,
          "tg_status": 1,
          "web_status": 1,
          "tg_biz_id": "@username",
          "has_fb_token": true,
          "has_ig_token": false,
          "has_tg_token": true,
          "has_tg_biz": false,
          "has_web_domain": true
        }
      }
      */

      // Call targeted disconnect-data API (only fetches necessary fields)
      const response = await fetch('/api/disconnect-data')
      const responseData = await response.json()

      if (!response.ok) {
        console.error('Error fetching disconnect data:', responseData.error)
        setErrorMessage(`Failed to load connection data: ${responseData.error}`)
        return
      }

      const data = responseData.credentials

      // If no connected platforms, redirect to connect page
      const hasConnections = data.has_fb_token || data.has_ig_token || data.has_tg_token || data.has_tg_biz || data.has_web_domain
      if (!hasConnections) {
        window.location.href = '/dashboard/connect';
        return;
      }

      setCredentials(data)

      // Set connected platforms based on has_* flags from API
      const newConnectedPlatforms = {
        facebook: data.has_fb_token,
        instagram: data.has_ig_token,
        telegram: data.has_tg_token || data.has_tg_biz, // Check both regular and business connections
        web: data.has_web_domain
      }
      setConnectedPlatforms(newConnectedPlatforms)

      // Note: No longer populate input fields with token values for security
    } catch (error) {
      console.error('Unexpected error in fetchClientCredentials:', error)
      setErrorMessage('An unexpected error occurred. Please try again later.')
    } finally {
      setIsLoading(false)
    }
  }

  // Note: Removed webhook copy and edit mode functions since this page only handles disconnection

  // Show delete confirmation dialog
  const initiateDelete = (platform: string) => {
    setShowDeleteConfirmation({
      show: true,
      platform
    });
  };

  // Handle platform deletion after confirmation
  const handleDelete = async (platform: string) => {
    try {
      // Set deleting state
      setIsDeleting(true);

      /*
      // OLD APPROACH: Call Next.js API that handles everything
      const response = await fetch('/api/platform/disconnect-secure', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: platform
        }),
      });
      */

      // NEW APPROACH: Call Next.js API for auth, then delegate to external workflow
      const response = await fetch('/api/platform/disconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: platform
        }),
      });

      // Wait for a minimum of 1 second for UX purposes
      await new Promise(resolve => setTimeout(resolve, 1000));

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.error || `Failed to disconnect ${platform}`);
      }

      if (!responseData.success) {
        throw new Error(`Unexpected response from server when disconnecting ${platform}`);
      }

      // All database operations, cache updates, and external webhook calls are now handled by external workflow

      // Update connected platforms state
      setConnectedPlatforms(prev => ({
        ...prev,
        [platform]: false
      }));

      // Show success message
      setSuccessMessage(`Successfully disconnected ${platform}!`);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 1500);

    } catch (error) {
      console.error(`Error disconnecting ${platform}:`, error);
      setErrorMessage(`Failed to disconnect ${platform}: ${error instanceof Error ? error.message : 'Unknown error'}`);

      // Clear error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 3000);
    } finally {
      // Reset states
      setIsDeleting(false);
      setShowDeleteConfirmation({ show: false, platform: null });
    }
  };

  // Note: Removed unused connect functions since this is an edit page for disconnection only

  // Don't render anything server-side
  if (!isClient) return null

  return (
    <div className={`min-h-screen ${themeConfig.pageBackground} flex flex-col relative`}>
      {/* Background effects (only for dark theme) */}
      {themeConfig.backgroundEffects}

      {/* Modern Glassmorphism Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.headerBackground} rounded-2xl px-4 py-3 border ${themeConfig.headerBorder} ${themeConfig.headerHoverBorder} transition-all duration-300 overflow-hidden`}
          >

            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <LinkButton 
                href="/dashboard" 
                variant="ghost" 
                className="p-0 hover:bg-transparent active:scale-95 focus:outline-none focus:ring-0 focus:border-none"
              >
                <Image
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  width={32}
                  height={32}
                  className="h-8 w-auto transition-transform duration-300 hover:scale-105"
                />
              </LinkButton>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-8">
        {/* Main content animations commented out as requested */}
        <div>

          {/* Content header with title and back button */}
          <div className="mb-12">
            <div className="flex items-center justify-between mb-2">
              <LinkButton
                href="/dashboard/connect"
                variant="secondary"
                size="sm"
                className="inline-flex items-center text-sm focus:outline-none focus:ring-0 focus:border-none"
                leftIcon={
                  <svg
                    className="w-4 h-4 -ml-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                }
              >
                {t('back')}
              </LinkButton>

              <h1 className={`text-2xl md:text-3xl font-extrabold font-title ${themeConfig.textPrimary}`}>
                {t('edit_connections')}
              </h1>

              {/* Empty div for balanced spacing */}
              <div className="w-10"></div>
            </div>
          </div>

          {/* Loading Indicator */}
          {isLoading && (
            <div className={`mb-6 p-4 bg-jade-purple/70 border border-jade-purple/30 rounded-lg ${themeConfig.textPrimary} flex items-center`}>
              <svg className={`animate-spin -ml-1 mr-3 h-5 w-5 ${themeConfig.textPrimary}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p className="text-sm font-body">{t('loading_connection_details')}</p>
            </div>
          )}

          {/* Error Message - Positioned in the middle of the window */}
          {errorMessage && (
            <div
              className={`fixed inset-0 flex items-center justify-center z-50 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'}`}
            >
              <div
                className={`${themeConfig.errorBackground} border ${themeConfig.errorBorder} rounded-xl p-6 max-w-md mx-auto transform transition-all relative ${themeConfig.errorShadow}`}
                style={{
                  willChange: 'transform, opacity'
                }}
                onClick={(e) => e.stopPropagation()}
              >
                {/* Close Button */}
                <button
                  onClick={() => setErrorMessage(null)}
                  className={`absolute top-3 right-3 w-8 h-8 ${theme === 'light' ? 'bg-red-100 hover:bg-red-200 text-red-600' : 'bg-red-500/20 hover:bg-jade-purple text-red-400 hover:text-white'} rounded-full flex items-center justify-center transition-all duration-200`}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>

                <div className="flex items-center justify-center mb-4">
                  <div className={`${theme === 'light' ? 'bg-red-100' : 'bg-red-500/20'} rounded-full p-3`}>
                    <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                </div>
                <h3 className={`text-xl font-semibold ${themeConfig.textPrimary} text-center mb-2 font-title`}>{t('error')}</h3>
                <p className={`text-center ${themeConfig.errorText} font-body`}>{errorMessage}</p>
              </div>
            </div>
          )}

          {/* Success Message - Positioned in the middle of the window */}
          {successMessage && (
            <div
              className={`fixed inset-0 flex items-center justify-center z-50 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'}`}
            >
              <div
                className={`${theme === 'light' ? 'bg-white border-green-300' : 'bg-black/80 border-green-500/50'} rounded-xl p-6 max-w-md mx-auto transform transition-all`}
                style={{
                  willChange: 'transform, opacity'
                }}
                onClick={(e) => e.stopPropagation()}
              >
                <div className="flex items-center justify-center mb-4">
                  <div className={`${theme === 'light' ? 'bg-green-100' : 'bg-green-500/20'} rounded-full p-3`}>
                    <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
                <h3 className={`text-xl font-semibold ${themeConfig.textPrimary} text-center mb-2 font-title`}>{t('success')}</h3>
                <p className={`text-center ${theme === 'light' ? 'text-green-700' : 'text-green-200'} font-body`}>{successMessage}</p>
              </div>
            </div>
          )}

          {/* Note: Removed update confirmation dialog since this page only handles disconnection */}

          {/* Delete Confirmation Dialog */}
          {showDeleteConfirmation.show && (
            <div
              className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
            >
              <div
                className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
                onClick={(e) => e.stopPropagation()}
              >
                {theme === 'dark' && (
                  <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
                )}
                <div className="relative z-10">
                  {isDeleting ? (
                    // Disconnecting animation
                    <>
                      <div className="flex items-center justify-center mb-4">
                        <div className={`${theme === 'light' ? 'bg-red-100' : 'bg-red-500/20'} rounded-full p-3`}>
                          <svg className="animate-spin w-8 h-8 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        </div>
                      </div>
                      <h3 className={`text-xl font-bold ${themeConfig.textPrimary} text-center mb-2 font-title`}>{t('disconnecting')}</h3>
                      <p className={`text-center ${themeConfig.textSecondary} font-body`}>
                        {t('please_wait_disconnect').replace('{platform}', showDeleteConfirmation.platform || '')}
                      </p>
                    </>
                  ) : (
                    // Normal confirmation dialog
                    <>
                      <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                        {t('confirm_disconnect')}
                      </h3>
                      <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                        {t('confirm_disconnect_message').replace('{platform}', showDeleteConfirmation.platform || '').replace('{type}', showDeleteConfirmation.platform === 'web' ? t('domain') : t('token'))}
                      </p>
                      <div className="flex justify-between w-full space-x-4">
                        <Button
                          onClick={() => setShowDeleteConfirmation({ show: false, platform: null })}
                          variant="secondary"
                          size="md"
                          className="flex-1"
                          disabled={isDeleting}
                        >
                          {t('cancel')}
                        </Button>
                        <Button
                          onClick={() => handleDelete(showDeleteConfirmation.platform!)}
                          variant="danger"
                          size="md"
                          className="flex-1"
                          disabled={isDeleting}
                        >
                          {t('disconnect')}
                        </Button>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Connected Platforms Section */}
          {!isLoading && (
            <div className="space-y-4">
              {/* Facebook */}
              {connectedPlatforms.facebook && (
                <div className={`${themeConfig.cardBackground} border ${themeConfig.cardBorder} rounded-lg p-4 flex items-center justify-between`}>
                  <div className="flex items-center">
                    <div className={`w-10 h-10 ${themeConfig.cardBackground} rounded-full flex items-center justify-center mr-4 text-blue-500 border ${themeConfig.cardBorder}`}>
                      <FaFacebookMessenger size={18} />
                    </div>
                    <div>
                      <h3 className={`font-semibold ${themeConfig.textPrimary} text-base font-title`}>{t('facebook_messenger')}</h3>
                      <p className={`${themeConfig.textMuted} text-xs font-body truncate max-w-[150px]`}>{t('name')}: {credentials?.fb_name || t('not_set')}</p>
                    </div>
                  </div>
                  <Button
                    onClick={() => initiateDelete('facebook')}
                    disabled={isDeleting}
                    variant="danger"
                    size="sm"
                    className="px-4 py-2"
                  >
                    {isDeleting ? t('disconnecting') : t('disconnect')}
                  </Button>
                </div>
              )}

              {/* Instagram */}
              {connectedPlatforms.instagram && (
                <div className={`${themeConfig.cardBackground} border ${themeConfig.cardBorder} rounded-lg p-4 flex items-center justify-between`}>
                  <div className="flex items-center">
                    <div className={`w-10 h-10 ${themeConfig.cardBackground} rounded-full flex items-center justify-center mr-4 text-pink-500 border ${themeConfig.cardBorder}`}>
                      <FaInstagram size={18} />
                    </div>
                    <div>
                      <h3 className={`font-semibold ${themeConfig.textPrimary} text-base font-title`}>{t('instagram')}</h3>
                      <p className={`${themeConfig.textMuted} text-xs font-body truncate max-w-[150px]`}>{t('name')}: {credentials?.ig_name || t('not_set')}</p>
                    </div>
                  </div>
                  <Button
                    onClick={() => initiateDelete('instagram')}
                    disabled={isDeleting}
                    variant="danger"
                    size="sm"
                    className="px-4 py-2"
                  >
                    {isDeleting ? t('disconnecting') : t('disconnect')}
                  </Button>
                </div>
              )}

              {/* Telegram */}
              {connectedPlatforms.telegram && (
                <div className={`${themeConfig.cardBackground} border ${themeConfig.cardBorder} rounded-lg p-4 flex items-center justify-between`}>
                  <div className="flex items-center">
                    <div className={`w-10 h-10 ${themeConfig.cardBackground} rounded-full flex items-center justify-center mr-4 text-blue-400 border ${themeConfig.cardBorder}`}>
                      <FaPaperPlane size={16} />
                    </div>
                    <div>
                      <h3 className={`font-semibold ${themeConfig.textPrimary} text-base font-title`}>
                        {t('telegram')} {credentials?.tg_biz_id ? '(Business)' : '(Bot)'}
                      </h3>
                      <p className={`${themeConfig.textMuted} text-xs font-body truncate max-w-[150px]`}>
                        {t('name')}: {credentials?.tg_name || t('not_set')}
                      </p>
                    </div>
                  </div>
                  <Button
                    onClick={() => initiateDelete('telegram')}
                    disabled={isDeleting}
                    variant="danger"
                    size="sm"
                    className="px-4 py-2"
                  >
                    {isDeleting ? t('disconnecting') : t('disconnect')}
                  </Button>
                </div>
              )}

              {/* Web API */}
              {connectedPlatforms.web && (
                <div className={`${themeConfig.cardBackground} border ${themeConfig.cardBorder} rounded-lg p-4 flex items-center justify-between`}>
                  <div className="flex items-center">
                    <div className={`w-10 h-10 ${themeConfig.cardBackground} rounded-full flex items-center justify-center mr-4 text-purple-500 border ${themeConfig.cardBorder}`}>
                      <FaGlobe size={18} />
                    </div>
                    <div>
                      <h3 className={`font-semibold ${themeConfig.textPrimary} text-base font-title`}>{t('web_api')}</h3>
                      <p className={`${themeConfig.textMuted} text-xs font-body truncate max-w-[150px]`}>{t('domain')}: {credentials?.web_domain || t('not_set')}</p>
                    </div>
                  </div>
                  <Button
                    onClick={() => initiateDelete('web')}
                    disabled={isDeleting}
                    variant="danger"
                    size="sm"
                    className="px-4 py-2"
                  >
                    {isDeleting ? t('disconnecting') : t('disconnect')}
                  </Button>
                </div>
              )}

              {/* No connected platforms message */}
              {Object.values(connectedPlatforms).filter(Boolean).length === 0 && (
                <div className={`${themeConfig.cardBackground} border ${themeConfig.cardBorder} rounded-lg p-6 text-center`}>
                  <p className={`${themeConfig.textMuted} font-body`}>{t('no_platforms_connected_edit')}</p>
                  <Link href="/dashboard/connect" className="mt-4 inline-block bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors font-body">
                    {t('connect_platforms')}
                  </Link>
                </div>
              )}
            </div>
          )}

          {/* <div className="mt-8 flex justify-center">
            <Link href="/dashboard/connect" className="bg-zinc-700 hover:bg-zinc-600 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors font-body">
              Back to Connections
            </Link>
          </div> */}
        </div>
      </div>
    </div>
  )
}