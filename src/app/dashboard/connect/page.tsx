'use client'

import Link from 'next/link'
import Image from 'next/image'
import { useState, useEffect, useRef } from 'react'
import { useAuth } from '@/context/AuthContext'
import { useDashboardData } from '@/hooks/useOptimizedData'
import Footer from '@/components/Footer'
import ConnectPageSkeleton from '@/components/ConnectPageSkeleton'
import { FaPaperPlane, FaFacebookMessenger, FaInstagram, FaGlobe, FaChevronDown } from 'react-icons/fa'
import { FaTiktok } from 'react-icons/fa6'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { Button, LinkButton } from '@/components/ui'

type WebhookState = {
  [key: string]: {
    isCopied: boolean;
  };
};

// Define platform type
type Platform = 'facebook' | 'instagram' | 'whatsapp' | 'telegram' | 'web' | 'tiktok';

// Define sanitized client credentials type (from API)
type ClientCredentials = {
  id: number;
  client_id: string;
  fb_name?: string;
  ig_name?: string;
  tg_name?: string;
  wa_name?: string;
  web_name?: string;
  web_domain?: string;
  fb_status?: number;
  ig_status?: number;
  wa_status?: number;
  tg_status?: number;
  web_status?: number;
  tg_biz_id?: string;
  // Security: URLs and tokens are not exposed, only boolean flags
  has_fb_token: boolean;
  has_ig_token: boolean;
  has_wa_token: boolean;
  has_tg_token: boolean;
  has_tg_biz: boolean;
  has_web_domain: boolean;
};

export default function ConnectAccountsPage() {
  const { user: _ } = useAuth() // Unused but kept for context
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Use dashboard cache for client info
  const { data: dashboardData } = useDashboardData()
  const clientInfo = dashboardData?.clientInfo

  // State variables
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [isClient, setIsClient] = useState(false)
  const [credentials, setCredentials] = useState<ClientCredentials | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [connectingPlatform, setConnectingPlatform] = useState<string | null>(null)
  const [isConnecting, setIsConnecting] = useState(false) // For confirmation popup spinning
  const [selectedPlatform, setSelectedPlatform] = useState<Platform>('telegram')
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [credentialsLoaded, setCredentialsLoaded] = useState<boolean>(false)

  const [availablePlatforms, setAvailablePlatforms] = useState<Platform[]>([])
  const [webhookStates, setWebhookStates] = useState<WebhookState>({
    'fb-webhook': { isCopied: false },
    'ig-webhook': { isCopied: false },
    'wa-webhook': { isCopied: false },
    'web-webhook': { isCopied: false },
    'privacy-policy': { isCopied: false },
    'telegram-bot-name': { isCopied: false }
  })
  const [showConfirmation, setShowConfirmation] = useState<{show: boolean, platform: string | null, action?: string | null}>({
    show: false,
    platform: null,
    action: null
  })

  // Track connected platforms
  const [connectedPlatforms, setConnectedPlatforms] = useState<{[key: string]: boolean}>({
    facebook: false,
    instagram: false,
    whatsapp: false,
    telegram: false,
    web: false
  })

  // Track platform status (enabled/disabled)
  const [platformStatus, setPlatformStatus] = useState<{[key: string]: boolean}>({
    facebook: false,
    instagram: false,
    whatsapp: false,
    telegram: false,
    web: false
  })

  // Store token values (used to track token changes)
  const [tokenValues, setTokenValues] = useState<{[key: string]: string}>({
    facebook: '',
    instagram: '',
    whatsapp: '',
    telegram: ''
  })



  // We're using password input type to mask tokens, no need for additional state

  // Refs for input fields
  const facebookTokenRef = useRef<HTMLInputElement>(null)
  const instagramTokenRef = useRef<HTMLInputElement>(null)
  const instagramIdRef = useRef<HTMLInputElement>(null)
  const telegramTokenRef = useRef<HTMLInputElement>(null)
  const telegramBusinessUsernameRef = useRef<HTMLInputElement>(null)
  const webDomainRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // State for Telegram connection type (bot or business)
  const [telegramConnectionType, setTelegramConnectionType] = useState<'bot' | 'business'>('business')

  // Platform data with icons and names
  const platforms = [
    { id: 'telegram', name: t('telegram'), icon: <FaPaperPlane size={16} className="text-blue-400" /> },
    { id: 'facebook', name: t('facebook_messenger'), icon: <FaFacebookMessenger size={18} className="text-blue-500" /> },
    { id: 'instagram', name: t('instagram'), icon: <FaInstagram size={18} className="text-pink-500" /> },
    // { id: 'whatsapp', name: 'WhatsApp', icon: <FaWhatsapp size={18} className="text-green-500" /> },
    { id: 'web', name: t('web_api_coming_soon_short'), icon: <FaGlobe size={18} className="text-purple-500" />, disabled: true },
    { id: 'tiktok', name: t('tiktok_coming_soon_short'), icon: <FaTiktok size={18} className="text-white" />, disabled: true }
  ]

  // Handle platform selection
  const handleSelectPlatform = (platform: Platform) => {
    // First close the dropdown immediately for better responsiveness
    setIsDropdownOpen(false)
    // Then update the selected platform (this will trigger a re-render)
    // No need for setTimeout, we'll handle this with proper animations
    setSelectedPlatform(platform)
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Disable page scroll when any popup is open
  useEffect(() => {
    if (errorMessage || successMessage || isLoading || showConfirmation.show) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [errorMessage, successMessage, isLoading, showConfirmation.show])

  // Set isClient to true when component mounts and fetch credentials
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Wait for dashboard data to load before fetching connection data
  useEffect(() => {
    if (clientInfo?.client_id) {
      fetchConnectionData();
    }
  }, [clientInfo?.client_id, clientInfo?.plan_type])

  // State for connection limit
  const [connectionLimit, setConnectionLimit] = useState<number>(1);
  const [connectionLimitLoaded, setConnectionLimitLoaded] = useState<boolean>(false);

  // Get connection limit
  const getConnectionLimit = () => {
    return connectionLimit;
  };

  // Fetch connection data (credentials + limit) via consolidated API (Security Enhanced)
  const fetchConnectionData = async () => {
    try {
      setIsLoading(true)

      // Wait for dashboard data to load before fetching connection data
      if (!clientInfo?.client_id) {
        setIsLoading(false)
        return
      }

      // Call consolidated connection-data API
      const response = await fetch('/api/connection-data')
      const responseData = await response.json()

      if (!response.ok) {
        console.error('Error fetching connection data:', responseData.error)
        setErrorMessage(`${t('failed_load_connection_data')}: ${responseData.error}`)
        return
      }

      // Set connection limit
      const limit = responseData.limit || 1;
      setConnectionLimit(limit);
      setConnectionLimitLoaded(true);

      // Set credentials data
      const data = responseData.credentials
      setCredentials(data)

      // Update token values (using has_* flags from API)
      const newTokenValues = {
        facebook: '',
        instagram: '',
        whatsapp: '',
        telegram: ''
      }
      setTokenValues(newTokenValues)

      // Set connected platforms based on has_* flags from API
      const newConnectedPlatforms = {
        facebook: data.has_fb_token,
        instagram: data.has_ig_token,
        whatsapp: data.has_wa_token,
        telegram: data.has_tg_token || data.has_tg_biz, // Check both regular and business connections
        web: data.has_web_domain
      }
      setConnectedPlatforms(newConnectedPlatforms)

      // Set platform status based on status fields (default to 1/true if status field is not present)
      // For Telegram, use tg_status for both bot and business connections
      const telegramConnected = data.has_tg_token || data.has_tg_biz;
      const telegramStatus = telegramConnected ? (data.tg_status === undefined ? true : data.tg_status === 1) : false;

      const newPlatformStatus = {
        facebook: data.fb_status === undefined ? true : data.fb_status === 1,
        instagram: data.ig_status === undefined ? true : data.ig_status === 1,
        whatsapp: data.wa_status === undefined ? true : data.wa_status === 1,
        telegram: telegramStatus,
        web: data.web_status === undefined ? true : data.web_status === 1
      }
      setPlatformStatus(newPlatformStatus)

      // Determine which platforms are already connected
      const connectedPlatformsList: Platform[] = [];
      if (data.has_fb_token) connectedPlatformsList.push('facebook');
      if (data.has_ig_token) connectedPlatformsList.push('instagram');
      // if (data.has_wa_token) connectedPlatformsList.push('whatsapp');
      // Check for both regular Telegram bot and Telegram Business connections
      if (data.has_tg_token || data.has_tg_biz) connectedPlatformsList.push('telegram');

      // Consider web as connected if there's a web_domain
      if (data.has_web_domain) connectedPlatformsList.push('web');

      // Set available platforms (all platforms except those already connected or disabled)
      const allPlatforms: Platform[] = ['telegram', 'facebook', 'instagram', /* 'whatsapp', */ 'web', 'tiktok'];
      const available = allPlatforms.filter(p =>
        !connectedPlatformsList.includes(p) || p === 'tiktok'
      );
      setAvailablePlatforms(available);

      // Set the default selected platform to the first available one
      if (available.length > 0 && available[0] !== 'tiktok') {
        setSelectedPlatform(available[0]);
      }

      // Note: We no longer populate input fields with token values for security
    } catch (error) {
      console.error('Unexpected error in fetchConnectionData:', error)
      setErrorMessage(t('unexpected_error'))
    } finally {
      setIsLoading(false)
      setCredentialsLoaded(true)
    }
  }

  // Helper function to check if all data is loaded
  const isDataReady = () => {
    return credentialsLoaded && connectionLimitLoaded && clientInfo?.client_id;
  }



  const handleCopyWebhook = async (id: string) => {
    try {
      let url = '';

      // For privacy policy, use direct URL
      if (id === 'privacy-policy') {
        url = 'https://www.chhlatbot.com/privacy';
      } else {
        // For webhook URLs, fetch from secure API
        const response = await fetch('/api/webhook-url', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ webhookId: id }),
        });

        if (!response.ok) {
          console.error('Failed to fetch webhook URL');
          setErrorMessage(t('failed_to_copy'));
          return;
        }

        const data = await response.json();
        url = data.url;
      }

      // Try using the newer navigator.clipboard API first
      try {
        await navigator.clipboard.writeText(url);
      } catch (clipboardError) {
        // Fallback for older browsers or when Clipboard API fails
        const textArea = document.createElement('textarea');
        textArea.value = url;
        textArea.style.position = 'fixed';
        textArea.style.left = '-9999px';
        textArea.style.top = '0';
        document.body.appendChild(textArea);
        
        try {
          textArea.select();
          textArea.setSelectionRange(0, 99999); // For mobile devices
          const successful = document.execCommand('copy');
          if (!successful) throw new Error('Copy command failed');
        } finally {
          document.body.removeChild(textArea);
        }
      }

      // Show success state
      setWebhookStates(prev => ({
        ...prev,
        [id]: { isCopied: true }
      }));

      // Show success message
      // setSuccessMessage(null);

      // Reset copy status and clear success message after 2 seconds
      setTimeout(() => {
        setWebhookStates(prev => ({
          ...prev,
          [id]: { isCopied: false }
        }));
        // setSuccessMessage(null);
      }, 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
      setErrorMessage(t('failed_to_copy'));
      
      // Clear error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 3000);
    }
  };

  // Handle copying bot name for Telegram Business
  const handleCopyBotName = async () => {
    try {
      const botName = 'yourchhlatbot';
      await navigator.clipboard.writeText(botName);

      // Show success state using existing webhook states
      setWebhookStates(prev => ({
        ...prev,
        'telegram-bot-name': { isCopied: true }
      }));

      // Reset copy status after 1.5 seconds
      setTimeout(() => {
        setWebhookStates(prev => ({
          ...prev,
          'telegram-bot-name': { isCopied: false }
        }));
      }, 1500);
    } catch (err) {
      console.error('Failed to copy bot name:', err);
    }
  };

  // We've removed the toggleWebhookVisibility function as it's no longer needed

  // Handle secure token input changes
  const handleTokenChange = (platform: string, value: string) => {
    // Store the actual token value in the ref
    switch (platform) {
      case 'facebook':
        if (facebookTokenRef.current) facebookTokenRef.current.value = value;
        break;
      case 'instagram':
        if (instagramTokenRef.current) instagramTokenRef.current.value = value;
        break;
      /* case 'whatsapp':
        if (whatsappTokenRef.current) whatsappTokenRef.current.value = value;
        break; */
      case 'telegram':
        if (telegramTokenRef.current) telegramTokenRef.current.value = value;
        break;
    }
  }

  // Initiate toggle platform status - show confirmation popup
  const initiateTogglePlatformStatus = (platform: string) => {
    // Get current status
    const currentStatus = platformStatus[platform];
    const newStatus = !currentStatus;

    // Show confirmation dialog
    setShowConfirmation({
      show: true,
      platform,
      action: newStatus ? 'enable' : 'disable'
    });
  };

  // Handle toggling platform status after confirmation - simplified with new API
  const handleTogglePlatformStatus = async (platform: string) => {
    try {
      // Set connecting state for popup animation
      setIsConnecting(true);

      // Get current status
      const currentStatus = platformStatus[platform];
      const newStatus = !currentStatus;

      // Call the new centralized toggle API
      const response = await fetch('/api/platform/toggle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: platform,
          status: newStatus
        }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        // Handle specific error messages from backend
        setErrorMessage(responseData.error || `Failed to update ${platform} status`);
        return;
      }

      if (!responseData.success) {
        setErrorMessage(`Unexpected response when updating ${platform} status`);
        return;
      }

      // API call successful - update local state and show success

      // Update local state
      setPlatformStatus(prev => ({
        ...prev,
        [platform]: newStatus
      }));

      // Show success message
      const status = newStatus ? t('enable').toLowerCase() : t('disable').toLowerCase();
      setSuccessMessage(t('platform_status_changed').replace('{platform}', platform).replace('{status}', status));

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 1500);

    } catch (error) {
      console.error(`Error toggling ${platform} status:`, error);
      setErrorMessage(`Failed to update ${platform} status`);

      // Clear error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 3000);
    } finally {
      setIsConnecting(false);
      setShowConfirmation({ show: false, platform: null });
    }
  };

  // Show confirmation dialog before connecting
  const initiateConnect = (platform: string) => {
    // Don't proceed if data is not ready yet
    if (!isDataReady()) {
      setErrorMessage(t('loading'));
      setTimeout(() => {
        setErrorMessage(null);
      }, 2000);
      return;
    }

    // Check if we've reached the connection limit
    const connectedCount = Object.values(connectedPlatforms).filter(Boolean).length;

    // Check if this platform is already connected
    const isAlreadyConnected = connectedPlatforms[platform];

    const connectionLimit = getConnectionLimit();
    if (connectedCount >= connectionLimit && !isAlreadyConnected) {
      setErrorMessage(t('connection_limit_reached_error').replace('{limit}', connectionLimit.toString()));

      // Auto-dismiss error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 3000);

      return;
    }

    // Get token or domain from input ref
    let value = '';
    let instagramId = '';

    switch (platform) {
      case 'facebook':
        value = facebookTokenRef.current?.value || '';
        break;
      case 'instagram':
        value = instagramTokenRef.current?.value || '';
        instagramId = instagramIdRef.current?.value || '';
        break;
      /* case 'whatsapp':
        value = whatsappTokenRef.current?.value || '';
        whatsappId = whatsappIdRef.current?.value || '';
        break; */
      case 'telegram':
        if (telegramConnectionType === 'bot') {
          value = telegramTokenRef.current?.value || '';
        } else {
          // For Telegram Business, get username instead of token
          value = telegramBusinessUsernameRef.current?.value || '';
        }
        break;
      case 'web':
        value = webDomainRef.current?.value || '';
        break;
    }

    if (!value || value.trim() === '') {
      let errorKey = 'enter_valid_token';
      if (platform === 'web') {
        errorKey = 'enter_valid_domain';
      } else if (platform === 'telegram' && telegramConnectionType === 'business') {
        errorKey = 'enter_valid_telegram_username';
      }
      setErrorMessage(t(errorKey).replace('{platform}', platform));

      // Auto-dismiss error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 1000);

      return;
    }

    // Check for Instagram ID
    if (platform === 'instagram' && (!instagramId || instagramId.trim() === '')) {
      setErrorMessage(t('enter_valid_page_id'));

      // Auto-dismiss error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 1000);

      return;
    }

    // Check for WhatsApp ID
    /* if (platform === 'whatsapp' && (!whatsappId || whatsappId.trim() === '')) {
      setErrorMessage('Please enter a valid ID for WhatsApp.');

      // Auto-dismiss error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 1000);

      return;
    } */

    // Show confirmation dialog
    setShowConfirmation({
      show: true,
      platform,
      action: null // No action means it's a connect operation
    });
  };

  // Handle platform connection after confirmation
  const handleConnect = async (platform: string) => {
    try {
      // Set connecting state for popup animation
      setIsConnecting(true);

      // Clear any previous messages
      setErrorMessage(null);
      setSuccessMessage(null);

      // Get client ID from dashboard cache
      const clientId = clientInfo?.client_id;

      if (!clientId) {
        setErrorMessage(t('client_id_not_found'));
        setIsConnecting(false);
        setShowConfirmation({ show: false, platform: null });
        return;
      }

      // Get token or domain from input ref
      let token = '';
      let domain = '';
      let webhookUrl = '';
      let isWebPlatform = platform === 'web';

      switch (platform) {
        case 'facebook':
          token = facebookTokenRef.current?.value || '';
          webhookUrl = ''; // Backend will fetch the actual webhook URL
          break;
        case 'instagram':
          token = instagramTokenRef.current?.value || '';
          webhookUrl = ''; // Backend will fetch the actual webhook URL
          break;
        /* case 'whatsapp':
          token = whatsappTokenRef.current?.value || '';
          webhookUrl = ''; // Backend will fetch the actual webhook URL
          break; */
        case 'telegram':
          if (telegramConnectionType === 'bot') {
            token = telegramTokenRef.current?.value || '';
            webhookUrl = ''; // Backend will fetch the actual webhook URL
          } else {
            // For Telegram Business, use username as URL and leave token empty
            const username = telegramBusinessUsernameRef.current?.value || '';
            // Ensure username starts with @
            const formattedUsername = username.startsWith('@') ? username : `@${username}`;
            token = ''; // Leave token empty for business
            webhookUrl = formattedUsername; // Special case: Use username as URL for Telegram Business
          }
          break;
        case 'web':
          domain = webDomainRef.current?.value || '';
          webhookUrl = ''; // Backend will fetch the actual webhook URL
          break;
        default:
          webhookUrl = '';
      }

      // Check validation before proceeding - handle Telegram Business first
      if (platform === 'telegram' && telegramConnectionType === 'business') {
        // For Telegram Business, check username instead of token
        const username = telegramBusinessUsernameRef.current?.value || '';
        if (!username || username.trim() === '') {
          setErrorMessage(t('enter_valid_telegram_username'));
          setIsConnecting(false);
          setShowConfirmation({ show: false, platform: null });

          // Auto-dismiss error message after 3 seconds
          setTimeout(() => {
            setErrorMessage(null);
          }, 3000);

          return;
        }
      } else if (isWebPlatform) {
        // Check domain for web platforms
        if (!domain || domain.trim() === '') {
          setErrorMessage(t('enter_valid_domain').replace('{platform}', platform));
          setIsConnecting(false);
          setShowConfirmation({ show: false, platform: null });

          // Auto-dismiss error message after 3 seconds
          setTimeout(() => {
            setErrorMessage(null);
          }, 1500);

          return;
        }
      } else {
        // For all other platforms (including Telegram Bot), check token
        if (!token || token.trim() === '') {
          setErrorMessage(t('enter_valid_token').replace('{platform}', platform));
          setIsConnecting(false);
          setShowConfirmation({ show: false, platform: null });

          // Auto-dismiss error message after 3 seconds
          setTimeout(() => {
            setErrorMessage(null);
          }, 3000);

          return;
        }
      }

      // Set connecting platform
      setConnectingPlatform(platform);

      // Prepare request body
      let platformType = platform;

      // For Telegram Business, use telegram_biz as type
      if (platform === 'telegram' && telegramConnectionType === 'business') {
        platformType = 'telegram_biz';
      }

      // For Instagram, append the page ID to the type parameter if available
      if (platform === 'instagram' && instagramIdRef.current?.value) {
        platformType = `${platform}/${instagramIdRef.current.value}`;
      }

      // For WhatsApp, append the ID to the type parameter if available
      /* if (platform === 'whatsapp' && whatsappIdRef.current?.value) {
        platformType = `${platform}/${whatsappIdRef.current.value}`;
      } */

      const requestBody: any = {
        client_id: clientId,
        webhook_url: webhookUrl,
        token: isWebPlatform ? domain : token,
        type: platformType
      };

      // For Telegram Business, the webhook_url contains the username and token is empty
      // No additional fields needed as the username is already in webhook_url

      // Send to API endpoint
      const response = await fetch('/api/platform/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      // Wait for a minimum of 1 second for UX purposes
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Parse the response
      const responseData = await response.json();

      if (!response.ok) {
        // Map error codes to translation keys
        let errorKey = 'failed_connect_platform'; // Default error key

        if (responseData.code) {
          switch (responseData.code) {
            case 'INVALID_TOKEN':
              errorKey = 'invalid_token_error';
              break;
            case 'ALREADY_USE_FOR_TRIAL':
              errorKey = 'already_use_for_trial_error';
              break;
            case 'STILL_CONNECTED':
              errorKey = 'still_connected_error';
              break;
            // Keep the default for unknown error codes
          }
        }

        // Translate the error message using the appropriate key
        setErrorMessage(t(errorKey).replace('{platform}', platform));
        setIsConnecting(false);
        setShowConfirmation({ show: false, platform: null });

        // Auto-dismiss error message after 3 seconds
        setTimeout(() => {
          setErrorMessage(null);
        }, 5000);

        return;
      }

      // Make sure it's a success
      if (!responseData.success) {
        setErrorMessage(t('unexpected_server_response').replace('{platform}', platform));
        setIsConnecting(false);
        setShowConfirmation({ show: false, platform: null });

        // Auto-dismiss error message after 3 seconds
        setTimeout(() => {
          setErrorMessage(null);
        }, 3000);

        return;
      }

      // Show success message
      setSuccessMessage(t('successfully_connected').replace('{platform}', platform));
      setIsConnecting(false);
      setShowConfirmation({ show: false, platform: null });

      // Wait for success message to be shown, then reload the page
      setTimeout(() => {
        window.location.reload();
      }, 1500);

    } catch (error) {
      console.error(`Error connecting ${platform}:`, error);
      setErrorMessage(t('failed_connect_platform').replace('{platform}', platform));
      setIsConnecting(false);
      setShowConfirmation({ show: false, platform: null });

      // Clear error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 3000);
    } finally {
      // Reset connecting platform (keep this for the individual button animations)
      setConnectingPlatform(null);
    }
  };

  // Don't render anything server-side
  if (!isClient) return null

  return (
    <div className={`${themeConfig.pageBackground} pb-16`}>
      {/* Conditional background effects based on theme */}
      {themeConfig.backgroundEffects}

      {/* Modern Glassmorphism Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.headerBackground} rounded-2xl px-4 py-3 border ${themeConfig.headerBorder} ${themeConfig.headerHoverBorder} transition-all duration-300 overflow-hidden`}
          >

            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <LinkButton 
                href="/dashboard" 
                variant="ghost" 
                className="p-0 hover:bg-transparent active:scale-95 focus:outline-none focus:ring-0 focus:border-none"
              >
                <Image
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  width={32}
                  height={32}
                  className="h-8 w-auto transition-transform duration-300 hover:scale-105"
                />
              </LinkButton>
            </div>
          </div>
        </div>
      </header>

      {/* Error Message - Positioned in the middle of the window */}
      {errorMessage && (
        <div
          className={`fixed inset-0 flex items-center justify-center z-50 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'}`}
        >
          <div
            className={`${themeConfig.errorBackground} border ${themeConfig.errorBorder} rounded-xl p-6 max-w-md mx-auto transform transition-all relative ${themeConfig.errorShadow}`}
            style={{
              willChange: 'transform, opacity'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <button
              onClick={() => setErrorMessage(null)}
              className={`absolute top-3 right-3 w-8 h-8 ${theme === 'light' ? 'bg-red-100 hover:bg-red-200 text-red-600 hover:text-red-700' : 'bg-red-500/20 hover:bg-red-500/30 text-red-400 hover:text-red-300'} rounded-full flex items-center justify-center transition-all duration-200`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="flex items-center justify-center mb-4">
              <div className={`${theme === 'light' ? 'bg-red-100' : 'bg-red-500/20'} rounded-full p-3`}>
                <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <h3 className={`text-xl font-semibold ${themeConfig.textPrimary} text-center mb-2 font-title`}>{t('error')}</h3>
            <p className={`text-center ${themeConfig.errorText} font-body`}>{errorMessage}</p>
          </div>
        </div>
      )}

      {/* Success Message - Positioned in the middle of the window */}
      {successMessage && (
        <div
          className={`fixed inset-0 flex items-center justify-center z-50 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'}`}
        >
          <div
            className={`${theme === 'light' ? 'bg-white border-green-300' : 'bg-black/80 border-green-500/50'} rounded-xl p-6 max-w-md mx-auto transform transition-all`}
            style={{
              willChange: 'transform, opacity'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-center mb-4">
              <div className={`${theme === 'light' ? 'bg-green-100' : 'bg-green-500/20'} rounded-full p-3`}>
                <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
            </div>
            <h3 className={`text-xl font-semibold ${themeConfig.textPrimary} text-center mb-2 font-title`}>{t('success')}</h3>
            <p className={`text-center ${theme === 'light' ? 'text-green-700' : 'text-green-200'} font-body`}>{successMessage}</p>
          </div>
        </div>
      )}


      {/* Confirmation Dialog */}
      {showConfirmation.show && (
        <div
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
        >
          <div
            className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${
              showConfirmation.action === 'disable' 
                ? `${theme === 'light' ? 'bg-white border-red-300' : 'bg-red-900/[0.8] border-red-500/50'}` 
                : `${theme === 'light' ? 'bg-white border-gray-300' : 'bg-jade-purple/[0.8] border-jade-purple/50'}`
            }`}
            onClick={(e) => e.stopPropagation()}
          >
            {theme === 'dark' && (
              <div className={`absolute inset-0 rounded-2xl opacity-50 ${
                showConfirmation.action === 'disable' 
                  ? 'bg-gradient-to-br from-red-500/10 to-transparent' 
                  : 'bg-gradient-to-br from-jade-purple/10 to-transparent'
              }`}></div>
            )}
            <div className="relative z-10">
            {isConnecting ? (
              // Connecting animation
              <>
                <div className="flex items-center justify-center mb-4">
                  <div className={`${
                    showConfirmation.action === 'disable'
                      ? theme === 'light' ? 'bg-red-500/10' : 'bg-red-500/20'
                      : theme === 'light' ? 'bg-jade-purple/10' : 'bg-jade-purple/20'
                  } rounded-full p-3`}>
                    <svg className={`animate-spin w-8 h-8 ${
                      showConfirmation.action === 'disable' ? 'text-red-500' : 'text-jade-purple'
                    }`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                </div>
                <h3 className={`text-xl font-bold ${themeConfig.textPrimary} text-center mb-2 font-title`}>{t('connecting')}</h3>
                <p className={`text-center ${themeConfig.textSecondary} font-body`}>
                  {t('please_wait_processing')} {showConfirmation.platform}...
                </p>
              </>
            ) : (
              // Normal confirmation dialog
              <>
                <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                  {showConfirmation.action ? t('confirm_status_change') : t('confirm_connection')}
                </h3>
                <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                  {showConfirmation.action ? (
                    t('confirm_action_platform').replace('{action}', showConfirmation.action).replace('{platform}', showConfirmation.platform || '')
                  ) : (
                    showConfirmation.platform === 'web'
                      ? t('confirm_connect_web').replace('{platform}', showConfirmation.platform || '')
                      : t('confirm_connect_platform').replace('{platform}', showConfirmation.platform || '')
                  )}
                </p>
                <div className="flex justify-between w-full space-x-4">
                  <Button
                    onClick={() => setShowConfirmation({ show: false, platform: null })}
                    variant="secondary"
                    size="md"
                    className="flex-1"
                    disabled={isConnecting}
                  >
                    {t('cancel')}
                  </Button>
                  <Button
                    onClick={() => {
                      if (showConfirmation.action) {
                        // Handle toggle action
                        handleTogglePlatformStatus(showConfirmation.platform!);
                      } else {
                        // Handle connect action
                        handleConnect(showConfirmation.platform!);
                      }
                    }}
                    variant={showConfirmation.action === 'disable' ? 'danger' : 'primary'}
                    size="md"
                    className="flex-1"
                    disabled={isConnecting}
                  >
                    {showConfirmation.action ? (showConfirmation.action === 'enable' ? t('enable') : t('disable')) : t('connect')}
                  </Button>
                </div>
              </>
            )}
            </div>
          </div>
        </div>
      )}

      <div className="flex-grow container mx-auto px-4 py-8">
        {/* Animation commented out as requested */}
        <div>
          {isLoading ? (
            <ConnectPageSkeleton />
          ) : (
            <>
              {/* Content header with title and back button */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-2">
                  <LinkButton
                    href="/dashboard"
                    variant="secondary"
                    size="sm"
                    className="inline-flex items-center text-sm"
                    leftIcon={
                      <svg
                        className="w-4 h-4 -ml-0.5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    }
                  >
                    {t('back')}
                  </LinkButton>

                  <h1 className={`text-2xl md:text-3xl font-extrabold font-title ${themeConfig.textPrimary}`}>
                    {t('connect_accounts')}
                  </h1>

                  {/* Empty div for balanced spacing */}
                  <div className="w-10"></div>
                </div>
              </div>

          {/* Connected Platforms Section - Moved to the top */}
          <div
            className={`relative ${themeConfig.cardBackground} rounded-2xl p-6 mb-6 border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} transition-all duration-300 group overflow-hidden`}>
            <div className="relative z-10">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h2 className={`text-xl font-bold font-title ${themeConfig.textPrimary}`}>
                  {t('connected_platforms')} ({isDataReady() ? `${Object.values(connectedPlatforms).filter(Boolean).length}/${getConnectionLimit()}` : '...'})
                </h2>
                {/* <p className="text-zinc-400 text-sm font-body mt-1">
                  {t('manage_connected_platforms')} ({Object.values(connectedPlatforms).filter(Boolean).length}/{connectionsLimit})
                </p> */}
              </div>
              {Object.values(connectedPlatforms).filter(Boolean).length > 0 && (
                <Link href="/dashboard/connect/editConnection" className={`${themeConfig.textSecondary} ${theme === 'light' ? 'hover:text-jade-purple-dark hover:bg-jade-purple/10' : 'hover:text-white hover:bg-white/10'} px-3 py-1 rounded transition-colors text-sm font-body`}>
                  {t('edit')}
                </Link>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
              {/* Facebook */}
              {connectedPlatforms.facebook && (
                <div className={`${themeConfig.cardBackground} border ${themeConfig.cardBorder} rounded-lg p-4 flex items-center justify-between`}>
                  <div className="flex items-center justify-between w-full">
                  <div className="flex items-center">
                    <div className={`w-10 h-10 ${theme === 'light' ? 'bg-gray-100' : 'bg-black/40'} rounded-full flex items-center justify-center mr-4 text-blue-500 border ${themeConfig.cardBorder}`}>
                      <FaFacebookMessenger size={18} />
                    </div>
                    <div>
                      <h3 className={`font-medium ${themeConfig.textPrimary} text-base font-title`}>{t('facebook_messenger')}</h3>
                      <p className={`${themeConfig.textSecondary} text-xs font-body truncate max-w-[150px]`}>{t('name')}: {credentials?.fb_name || t('not_set')}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
                      <input
                        type="checkbox"
                        id="toggle-facebook"
                        className="absolute w-6 h-6 opacity-0 z-10 cursor-pointer"
                        checked={platformStatus.facebook}
                        onChange={() => initiateTogglePlatformStatus('facebook')}
                      />
                      <label
                        htmlFor="toggle-facebook"
                        className={`block h-6 overflow-hidden rounded-full cursor-pointer transition-colors duration-200 ${platformStatus.facebook ? 'bg-green-500/30' : 'bg-gray-700'}`}
                      >
                        <span
                          className={`block h-6 w-6 rounded-full transform transition-all duration-200 ease-in-out ${platformStatus.facebook ? 'bg-green-500 translate-x-6 scale-105' : 'bg-white translate-x-0'}`}
                        />
                      </label>
                    </div>
                  </div>
                  </div>
                </div>
              )}

              {/* Instagram */}
              {connectedPlatforms.instagram && (
                <div className={`${themeConfig.cardBackground} border ${themeConfig.cardBorder} rounded-lg p-4 flex items-center justify-between`}>
                  <div className="flex items-center justify-between w-full">
                  <div className="flex items-center">
                    <div className={`w-10 h-10 ${theme === 'light' ? 'bg-gray-100' : 'bg-black/40'} rounded-full flex items-center justify-center mr-4 text-pink-500 border ${themeConfig.cardBorder}`}>
                      <FaInstagram size={18} />
                    </div>
                    <div>
                      <h3 className={`font-medium ${themeConfig.textPrimary} text-base font-title`}>{t('instagram')}</h3>
                      <p className={`${themeConfig.textSecondary} text-xs font-body truncate max-w-[150px]`}>{t('name')}: {credentials?.ig_name || t('not_set')}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
                      <input
                        type="checkbox"
                        id="toggle-instagram"
                        className="absolute w-6 h-6 opacity-0 z-10 cursor-pointer"
                        checked={platformStatus.instagram}
                        onChange={() => initiateTogglePlatformStatus('instagram')}
                      />
                      <label
                        htmlFor="toggle-instagram"
                        className={`block h-6 overflow-hidden rounded-full cursor-pointer transition-colors duration-200 ${platformStatus.instagram ? 'bg-green-500/30' : 'bg-gray-700'}`}
                      >
                        <span
                          className={`block h-6 w-6 rounded-full transform transition-all duration-200 ease-in-out ${platformStatus.instagram ? 'bg-green-500 translate-x-6 scale-105' : 'bg-white translate-x-0'}`}
                        />
                      </label>
                    </div>
                  </div>
                  </div>
                </div>
              )}

              {/* Telegram */}
              {connectedPlatforms.telegram && (
                <div className={`${themeConfig.secondCardBackground} border ${themeConfig.cardBorder} rounded-lg p-4 flex items-center justify-between`}>
                  <div className="flex items-center justify-between w-full">
                  <div className="flex items-center">
                    <div className={`w-10 h-10 ${theme === 'light' ? 'bg-gray-100' : 'bg-black/40'} rounded-full flex items-center justify-center mr-4 text-blue-400 border ${themeConfig.cardBorder}`}>
                      <FaPaperPlane size={16} />
                    </div>
                    <div>
                      <h3 className={`font-medium ${themeConfig.textPrimary} text-base font-title`}>
                        {t('telegram')} {credentials?.tg_biz_id ? '(Business)' : '(Bot)'}
                      </h3>
                      <p className={`${themeConfig.textSecondary} text-xs font-body truncate max-w-[150px]`}>
                        {t('name')}: {credentials?.tg_name || t('not_set')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
                      <input
                        type="checkbox"
                        id="toggle-telegram"
                        className="absolute w-6 h-6 opacity-0 z-10 cursor-pointer"
                        checked={platformStatus.telegram}
                        onChange={() => initiateTogglePlatformStatus('telegram')}
                      />
                      <label
                        htmlFor="toggle-telegram"
                        className={`block h-6 overflow-hidden rounded-full cursor-pointer transition-colors duration-200 ${platformStatus.telegram ? 'bg-green-500/30' : 'bg-gray-700'}`}
                      >
                        <span
                          className={`block h-6 w-6 rounded-full transform transition-all duration-200 ease-in-out ${platformStatus.telegram ? 'bg-green-500 translate-x-6 scale-105' : 'bg-white translate-x-0'}`}
                        />
                      </label>
                    </div>
                  </div>
                  </div>
                </div>
              )}

              {/* Web API */}
              {connectedPlatforms.web && (
                <div className={`${themeConfig.cardBackground} border ${themeConfig.cardBorder} rounded-lg p-4 flex items-center justify-between`}>
                  <div className="flex items-center justify-between w-full">
                  <div className="flex items-center">
                    <div className={`w-10 h-10 ${theme === 'light' ? 'bg-gray-100' : 'bg-black/40'} rounded-full flex items-center justify-center mr-4 text-purple-500 border ${themeConfig.cardBorder}`}>
                      <FaGlobe size={18} />
                    </div>
                    <div>
                      <h3 className={`font-medium ${themeConfig.textPrimary} text-base font-title`}>{t('web_api')}</h3>
                      <p className={`${themeConfig.textSecondary} text-xs font-body truncate max-w-[150px]`}>{t('domain')}: {credentials?.web_domain || t('not_set')}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
                      <input
                        type="checkbox"
                        id="toggle-web"
                        className="absolute w-6 h-6 opacity-0 z-10 cursor-pointer"
                        checked={platformStatus.web}
                        onChange={() => initiateTogglePlatformStatus('web')}
                      />
                      <label
                        htmlFor="toggle-web"
                        className={`block h-6 overflow-hidden rounded-full cursor-pointer transition-colors duration-200 ${platformStatus.web ? 'bg-green-500/30' : 'bg-gray-700'}`}
                      >
                        <span
                          className={`block h-6 w-6 rounded-full transform transition-all duration-200 ease-in-out ${platformStatus.web ? 'bg-green-500 translate-x-6 scale-105' : 'bg-white translate-x-0'}`}
                        />
                      </label>
                    </div>
                  </div>
                  </div>
                </div>
              )}

              {/* No connected platforms message */}
              {Object.values(connectedPlatforms).filter(Boolean).length === 0 && (
                <div className={`col-span-2 ${themeConfig.secondCardBackground} border ${themeConfig.cardBorder} rounded-lg p-6 text-center`}>
                  <p className={`${themeConfig.textSecondary} font-body`}>{t('no_platforms_connected')}</p>
                </div>
              )}
            </div>
            </div>
          </div>

          {/* Platform Selector Dropdown */}
          <div
            className={`relative ${themeConfig.cardBackground} rounded-2xl p-6 mb-6 border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} transition-all duration-300 group overflow-hidden`}
          >
            <div className="relative z-10">
            <h2 className={`text-xl font-bold mb-4 font-title ${themeConfig.textPrimary}`}>{t('select_platform')}</h2>
            {/* <p className="text-zinc-400 mb-6 font-body">
              {t('choose_platform_details')}
            </p> */}

            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => isDataReady() && Object.values(connectedPlatforms).filter(Boolean).length < getConnectionLimit() && setIsDropdownOpen(!isDropdownOpen)}
                className={`w-full ${themeConfig.inputBackground} border ${themeConfig.inputBorder} rounded-lg px-4 py-3 ${themeConfig.inputText} flex items-center justify-between focus:outline-none transition-colors ${
                  !isDataReady()
                    ? 'opacity-50 cursor-wait'
                    : Object.values(connectedPlatforms).filter(Boolean).length < getConnectionLimit()
                    ? `focus:border-gray-500 hover:border-gray-500`
                    : 'opacity-70 cursor-not-allowed'
                }`}
                disabled={!isDataReady() || Object.values(connectedPlatforms).filter(Boolean).length >= getConnectionLimit()}
              >
                {!isDataReady() ? (
                  <div className={`flex items-center ${themeConfig.textSecondary}`}>
                    <span className="font-body">{t('loading')}...</span>
                  </div>
                ) : Object.values(connectedPlatforms).filter(Boolean).length >= getConnectionLimit() ? (
                  <div className="flex items-center text-red-500">
                    <span className="font-body">{t('connection_limit_reached')}</span>
                  </div>
                ) : (
                  <>
                    <div className="flex items-center">
                      {platforms.find(p => p.id === selectedPlatform)?.icon}
                      <span className="ml-3 font-body">
                        {platforms.find(p => p.id === selectedPlatform)?.name}
                      </span>
                    </div>
                    <FaChevronDown className={`transition-transform duration-200 ${isDropdownOpen ? 'transform rotate-180' : ''}`} />
                  </>
                )}
              </button>

              {/* Animation commented out as requested */}
              <div
                className={`relative w-full ${themeConfig.secondCardBackground} border ${themeConfig.cardBorder} rounded-lg overflow-hidden mt-2 z-10 ${!isDropdownOpen ? 'hidden' : ''}`}
              >
                <ul>
                  {platforms
                    .filter(platform => availablePlatforms.includes(platform.id as Platform))
                    .map((platform) => (
                      <li key={platform.id}>
                        <button
                          onClick={() => !platform.disabled && handleSelectPlatform(platform.id as Platform)}
                          className={`w-full px-4 py-3 text-left flex items-center transition-colors ${
                            platform.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                          } ${
                            selectedPlatform === platform.id 
                              ? `${theme === 'light' ? 'bg-[#faf9f6] text-zinc-900' : 'bg-jade-purple/30 text-white'}` 
                              : `${theme === 'light' ? 'hover:bg-gray-200 text-zinc-900' : 'hover:bg-jade-purple/20 text-white'}`
                          }`}
                          disabled={platform.disabled}
                        >
                          {platform.icon}
                          <span className="ml-3 font-body">{platform.name}</span>
                        </button>
                      </li>
                    ))}
                </ul>
              </div>
            </div>
            </div>
          </div>

          {isDataReady() && availablePlatforms.length > 0 && availablePlatforms.some(p => p !== 'tiktok') &&
           Object.values(connectedPlatforms).filter(Boolean).length < getConnectionLimit() && (
            <div
              className={`relative ${themeConfig.cardBackground} rounded-2xl p-6 mb-12 border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} transition-all duration-300 group overflow-hidden`}
            >
              <div className="relative z-10">
              <div className="flex justify-between items-center mb-4">
                <div>
                  <h2 className={`text-xl font-bold font-title ${themeConfig.textPrimary}`}>{t('selected_platform')}</h2>
                  <p className={`${themeConfig.textSecondary} text-sm font-body mt-1`}>
                    {t('connect_platform_message').replace('{platform}', platforms.find(p => p.id === selectedPlatform)?.name || '')}
                  </p>
                </div>
              </div>



            <div className="space-y-6">

              {/* Facebook Messenger */}
              {selectedPlatform === 'facebook' && (
                <div className={`${themeConfig.secondCardBackground} border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} rounded-xl p-6 transition-all`}>
                <div className="flex items-center mb-4">
                  <div className={`w-10 h-10 ${theme === 'light' ? 'bg-gray-100' : 'bg-white/10'} rounded-full flex items-center justify-center mr-4 text-blue-500 border ${themeConfig.cardBorder}`} style={{ minWidth: '2.5rem' }}>
                    <FaFacebookMessenger size={18} />
                  </div>
                  <div style={{ flex: 1 }}>
                    <h3 className={`font-medium ${themeConfig.textPrimary} text-lg font-title`}>{t('facebook_messenger')}</h3>
                    <p className={`${themeConfig.textSecondary} text-sm font-body`}>{t('copy_webhook_provide_token')}</p>
                  </div>
                </div>
                <div className="space-y-3">

                  {/* Privacy Policy and Webhook URLs */}
                  <div className="flex flex-col sm:flex-row gap-3">
                    {/* Privacy Policy Link */}
                    <div className="relative w-full sm:w-[40%]">
                      <div className="flex items-center">
                        <button
                          className={`w-full ${themeConfig.inputBackground} border ${themeConfig.inputBorder} rounded-lg px-4 py-2.5 text-sm ${themeConfig.textSecondary} font-body flex items-center justify-between ${theme === 'light' ? 'hover:bg-gray-100' : 'hover:bg-black/40'} hover:border-gray-500 transition-colors cursor-pointer`}
                          onClick={() => handleCopyWebhook('privacy-policy')}
                          title="Click to copy Privacy Policy URL"
                        >
                          <span className="text-jade-purple truncate block">https://www.chhlatbot.com/privacy</span>
                          <span className={`${themeConfig.textSecondary} ml-2 flex-shrink-0 w-5 h-5 flex items-center justify-center`}>
                            {webhookStates['privacy-policy'].isCopied ? (
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                              </svg>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                <path strokeLinecap="round" strokeLinejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            )}
                          </span>
                        </button>
                      </div>
                    </div>

                    {/* Webhook URL */}
                    <div className="relative flex-1">
                      <div className="flex items-center">
                        <button
                          className={`w-full ${themeConfig.inputBackground} border ${themeConfig.inputBorder} rounded-lg px-4 py-2.5 text-sm ${themeConfig.textSecondary} font-body flex items-center justify-between ${theme === 'light' ? 'hover:bg-gray-100' : 'hover:bg-black/40'} hover:border-gray-500 transition-colors cursor-pointer`}
                          onClick={() => handleCopyWebhook('fb-webhook')}
                          title="Click to copy webhook URL"
                        >
                          <span>••••••••••••••••••</span>
                          <span className={`${themeConfig.textSecondary} ml-2 flex-shrink-0 w-5 h-5 flex items-center justify-center`}>
                            {webhookStates['fb-webhook'].isCopied ? (
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                              </svg>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                <path strokeLinecap="round" strokeLinejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            )}
                          </span>
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    {connectedPlatforms.facebook ? (
                      <>
                        <div className={`flex-1 ${themeConfig.inputBackground} border ${themeConfig.inputBorder} rounded-lg px-4 py-2.5 text-sm ${themeConfig.textSecondary} font-body`}
>
                          ••••••••••••••••••••••••••••••••••••••••••••••••••••
                        </div>
                        <button
                          className="bg-jade-purple/50 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                          disabled
                        >
                          {t('connected')}
                        </button>
                      </>
                    ) : (
                      <>
                        <div className="relative flex-1">
                          <input
                            ref={facebookTokenRef}
                            type="password"
                            placeholder={t('enter_access_token')}
                            defaultValue={''}
                            className={`w-full px-3 py-2 ${themeConfig.inputBackground} border ${themeConfig.inputBorder} rounded-lg ${themeConfig.inputText} font-body focus:outline-none focus:border-gray-500 hover:border-gray-500`}
                            style={{
                              fontSize: '16px'
                            }}
                            disabled={connectingPlatform === 'facebook'}
                            onChange={(e) => handleTokenChange('facebook', e.target.value)}
                          />
                        </div>
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={() => initiateConnect('facebook')}
                          disabled={connectingPlatform === 'facebook'}
                          isLoading={connectingPlatform === 'facebook'}
                          loadingText={t('connecting')}
                          className="px-5 py-2.5 md:px-8 md:min-w-[120px]"
                        >
                          {t('connect')}
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>
              )}

              {/* Instagram DM */}
              {selectedPlatform === 'instagram' && (
                <div className={`${themeConfig.secondCardBackground} border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} rounded-xl p-6 transition-all`}>
                <div className="flex items-center mb-4">
                  <div className={`w-10 h-10 ${theme === 'light' ? 'bg-gray-100' : 'bg-white/10'} rounded-full flex items-center justify-center mr-4 text-pink-500 border ${themeConfig.cardBorder}`} style={{ minWidth: '2.5rem' }}>
                    <FaInstagram size={20} />
                  </div>
                  <div style={{ flex: 1 }}>
                    <h3 className={`font-medium ${themeConfig.textPrimary} text-lg font-title`}>{t('instagram_dm')}</h3>
                    <p className={`${themeConfig.textSecondary} text-sm font-body`}>{t('copy_webhook_provide_token_id')}</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="relative">
                    <div className="flex items-center">
                      <button
                        className={`flex-1 ${themeConfig.inputBackground} border ${themeConfig.inputBorder} rounded-lg px-4 py-2.5 text-sm ${themeConfig.textSecondary} font-body flex items-center justify-between ${theme === 'light' ? 'hover:bg-gray-100' : 'hover:bg-black/40'} hover:border-gray-500 transition-colors cursor-pointer`}
                        onClick={() => handleCopyWebhook('ig-webhook')}
                        title="Click to copy webhook URL"
                      >
                        <span>••••••••••••••••••</span>
                        <span className={`${themeConfig.textSecondary} ml-2 flex-shrink-0 w-5 h-5 flex items-center justify-center`}>
                          {webhookStates['ig-webhook'].isCopied ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                              <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                              <path strokeLinecap="round" strokeLinejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          )}
                        </span>
                      </button>
                    </div>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-3">
                    {connectedPlatforms.instagram ? (
                      <>
                        <div className={`flex-1 ${themeConfig.inputBackground} border ${themeConfig.inputBorder} rounded-lg px-4 py-2.5 text-sm ${themeConfig.textSecondary} font-body overflow-hidden`}
                          title={`Token: ${tokenValues.instagram}`}>
                          <div className="truncate">•••••••••••••••••••••••••••</div>
                        </div>
                        <button
                          className="bg-jade-purple/50 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                          disabled
                        >
                          {t('connected')}
                        </button>
                      </>
                    ) : (
                      <>
                        <div className="relative w-full sm:w-[40%]">
                          <input
                            ref={instagramIdRef}
                            type="password"
                            placeholder={t('enter_id')}
                            defaultValue={''}
                            className={`w-full px-3 py-2 ${themeConfig.inputBackground} border ${themeConfig.inputBorder} rounded-lg ${themeConfig.inputText} font-body focus:outline-none focus:border-gray-500 hover:border-gray-500`}
                            style={{
                              fontSize: '16px'
                            }}
                            disabled={connectingPlatform === 'instagram'}
                          />
                        </div>
                        <div className="flex flex-col gap-2 flex-1">
                          <div className="relative">
                            <input
                              ref={instagramTokenRef}
                              type="password"
                              placeholder={t('enter_access_token')}
                              defaultValue={''}
                              className={`w-full px-3 py-2 ${themeConfig.inputBackground} border ${themeConfig.inputBorder} rounded-lg ${themeConfig.inputText} font-body focus:outline-none focus:border-gray-500 hover:border-gray-500`}
                              style={{
                                fontSize: '16px'
                              }}
                              disabled={connectingPlatform === 'instagram'}
                              onChange={(e) => handleTokenChange('instagram', e.target.value)}
                            />
                          </div>
                        </div>
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={() => initiateConnect('instagram')}
                          disabled={connectingPlatform === 'instagram'}
                          isLoading={connectingPlatform === 'instagram'}
                          loadingText={t('connecting')}
                          className="px-5 py-2.5 md:px-8 md:min-w-[120px]"
                        >
                          {t('connect')}
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>
              )}


              {/* Telegram */}
              {selectedPlatform === 'telegram' && (
                <div className={`${themeConfig.secondCardBackground} border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} rounded-xl p-6 transition-all`}>
                  <div className="flex items-center mb-4">
                    <div className={`w-10 h-10 ${theme === 'light' ? 'bg-gray-100' : 'bg-white/10'} rounded-full flex items-center justify-center mr-4 text-blue-400 border ${themeConfig.cardBorder}`} style={{ minWidth: '2.5rem' }}>
                      <FaPaperPlane size={16} />
                    </div>
                    <div style={{ flex: 1 }}>
                      <h3 className={`font-medium ${themeConfig.textPrimary} text-lg font-title`}>{t('telegram')}</h3>
                      <p className={`${themeConfig.textSecondary} text-sm font-body`}>{t('choose_connection_type')}</p>
                    </div>
                  </div>

                  {/* Tab Navigation - Simplified Glassmorphism Style */}
                  <div className="grid grid-cols-2 gap-2 mb-3">
                    <button
                      onClick={() => setTelegramConnectionType('business')}
                      className={`relative group p-3 rounded-xl border transition-all duration-300 overflow-hidden ${
                        telegramConnectionType === 'business'
                          ? `${themeConfig.connectTabCardBackground} ${themeConfig.connectTabCardBorder} ${themeConfig.textPrimary}`
                          : `${themeConfig.inputBackground} ${themeConfig.inputBorder} ${themeConfig.textSecondary} ${theme === 'light' ? 'hover:border-gray-500' : 'hover:text-white hover:bg-black/30 hover:border-gray-500'}`
                      }`}
                    >

                      <div className="relative z-10 text-center">
                        <h4 className="text-xs sm:text-base font-title">{t('telegram_business')}</h4>
                      </div>
                    </button>

                    <button
                      onClick={() => setTelegramConnectionType('bot')}
                      className={`relative group p-3 rounded-xl border transition-all duration-300 overflow-hidden ${
                        telegramConnectionType === 'bot'
                          ? `${themeConfig.connectTabCardBackground} ${themeConfig.connectTabCardBorder} ${themeConfig.textPrimary}`
                          : `${themeConfig.inputBackground} ${themeConfig.inputBorder} ${themeConfig.textSecondary} ${theme === 'light' ? 'hover:border-gray-500' : 'hover:text-white hover:bg-black/30 hover:border-gray-500'}`
                      }`}
                    >

                      <div className="relative z-10 text-center">
                        <h4 className="text-xs sm:text-base font-title">{t('telegram_bot')}</h4>
                      </div>
                    </button>
                  </div>

                  {/* Tab Content */}
                  {telegramConnectionType === 'bot' && (
                    <div className="space-y-2">
                      <div className="flex flex-col sm:flex-row gap-3">
                        {connectedPlatforms.telegram ? (
                          <>
                            <div className={`flex-1 ${themeConfig.inputBackground} border ${themeConfig.inputBorder} rounded-lg px-4 py-2.5 text-sm ${themeConfig.textSecondary} font-body overflow-hidden`}
>
                              <div className="truncate">••••••••••••••••••••••••••••••••••••••••</div>
                            </div>
                            <button
                              className="bg-jade-purple/50 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                              disabled
                            >
                              {t('connected')}
                            </button>
                          </>
                        ) : (
                          <>
                            <div className="relative flex-1">
                              <input
                                ref={telegramTokenRef}
                                type="password"
                                placeholder={t('enter_telegram_bot_token')}
                                defaultValue={''}
                                className={`w-full px-3 py-2 ${themeConfig.inputBackground} border ${themeConfig.inputBorder} rounded-lg ${themeConfig.inputText} font-body focus:outline-none focus:border-gray-500 hover:border-gray-500`}
                                style={{
                                  fontSize: '16px'
                                }}
                                disabled={connectingPlatform === 'telegram'}
                                onChange={(e) => handleTokenChange('telegram', e.target.value)}
                              />
                            </div>
                            <Button
                              variant="primary"
                              size="sm"
                              onClick={() => initiateConnect('telegram')}
                              disabled={connectingPlatform === 'telegram'}
                              isLoading={connectingPlatform === 'telegram'}
                              loadingText={t('connecting')}
                              className="px-5 py-2.5 md:px-8 md:min-w-[120px]"
                            >
                              {t('connect')}
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  )}

                  {telegramConnectionType === 'business' && (
                    <div className="space-y-2">
                      {/* Bot Name Copy Field */}
                      <div className="flex gap-3">
                        <div className="relative flex-1">
                          <button
                            className={`w-full ${themeConfig.inputBackground} border ${themeConfig.inputBorder} rounded-lg px-4 py-2.5 text-sm ${themeConfig.textPrimary} font-body flex items-center justify-between ${theme === 'light' ? 'hover:bg-gray-100' : 'hover:bg-black/40'} hover:border-gray-500 transition-colors cursor-pointer`}
                            onClick={handleCopyBotName}
                            title="Click to copy bot name"
                          >
                            <span className="text-jade-purple">yourchhlatbot</span>
                            <span className={`${themeConfig.textSecondary} transition-colors flex items-center justify-center`}>
                              {webhookStates['telegram-bot-name']?.isCopied ? (
                                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                  <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                                </svg>
                              ) : (
                                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                  <path strokeLinecap="round" strokeLinejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                </svg>
                              )}
                            </span>
                          </button>
                        </div>
                      </div>

                      {/* Username Input and Connect Button */}
                      <div className="flex flex-col sm:flex-row gap-3">
                        <div className="relative flex-1">
                          <input
                            ref={telegramBusinessUsernameRef}
                            type="text"
                            placeholder={t('telegram_username_placeholder')}
                            defaultValue={''}
                            className={`w-full px-3 py-2 ${themeConfig.inputBackground} border ${themeConfig.inputBorder} rounded-lg ${themeConfig.inputText} font-body focus:outline-none focus:border-gray-500 hover:border-gray-500`}
                            style={{
                              fontSize: '16px'
                            }}
                            onChange={(e) => {
                              // Ensure the input starts with @
                              if (!e.target.value.startsWith('@') && e.target.value.length > 0) {
                                e.target.value = '@' + e.target.value;
                              }
                            }}
                          />
                        </div>
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={() => initiateConnect('telegram')}
                          disabled={connectingPlatform === 'telegram'}
                          isLoading={connectingPlatform === 'telegram'}
                          loadingText={t('connecting')}
                          className="px-5 py-2.5 md:px-8 md:min-w-[120px]"
                        >
                          {t('connect')}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Web API */}
              {selectedPlatform === 'web' && (
                <div className={`${themeConfig.secondCardBackground} border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} rounded-xl p-6 transition-all`}>
                <div className="flex flex-col items-center">
                  <div className="flex items-center mb-4">
                    <div className={`w-10 h-10 ${theme === 'light' ? 'bg-gray-100' : 'bg-black/50'} rounded-full flex items-center justify-center mr-4 text-purple-500 border ${themeConfig.cardBorder}`} style={{ minWidth: '2.5rem' }}>
                      <FaGlobe size={20} />
                    </div>
                    <div>
                      <h3 className={`font-medium ${themeConfig.textPrimary} text-lg font-title`}>{t('web_api')}</h3>
                      <p className={`${themeConfig.textSecondary} text-sm font-body`}>{t('coming_soon')}</p>
                    </div>
                  </div>
                  <p className={`${themeConfig.textMuted} mt-2 font-body`}>{t('web_api_coming_soon')}</p>
                </div>
              </div>
              )}

              {/* Placeholder with improved styling */}
              {selectedPlatform === 'tiktok' && (
                <div className={`${themeConfig.cardBackground} border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} rounded-xl p-6 transition-all`}>
                <div className="flex flex-col items-center">
                  <div className="flex items-center mb-4">
                    <div className={`w-10 h-10 ${theme === 'light' ? 'bg-gray-100' : 'bg-black/50'} rounded-full flex items-center justify-center mr-4 ${themeConfig.textPrimary} border ${themeConfig.cardBorder}`} style={{ minWidth: '2.5rem' }}>
                      <FaTiktok size={20} />
                    </div>
                    <div>
                      <h3 className={`font-medium ${themeConfig.textPrimary} text-lg font-title`}>{t('tiktok')}</h3>
                      <p className={`${themeConfig.textSecondary} text-sm font-body`}>{t('coming_soon')}</p>
                    </div>
                  </div>
                  <p className={`${themeConfig.textMuted} mt-2 font-body`}>{t('more_platforms_coming_soon')}</p>
                </div>
              </div>
              )}
            </div>
            </div>
          </div>
          )}
            </>
          )}
        </div>
      </div>

      <Footer />
    </div>
  )
}
