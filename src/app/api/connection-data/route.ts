import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

// Define the sanitized credentials type (what we send to client)
type SanitizedCredentials = {
  id: number
  client_id: string
  fb_name?: string
  ig_name?: string
  tg_name?: string
  wa_name?: string
  web_name?: string
  web_domain?: string
  fb_status?: number
  ig_status?: number
  wa_status?: number
  tg_status?: number
  web_status?: number
  tg_biz_id?: string
  // Note: We don't expose URLs or tokens for security
  has_fb_token: boolean
  has_ig_token: boolean
  has_wa_token: boolean
  has_tg_token: boolean
  has_tg_biz: boolean
  has_web_domain: boolean
}

type ConnectionDataResponse = {
  limit: number
  plan_type: string
  credentials: SanitizedCredentials
}

export async function GET() {
  try {
    // Single authentication check
    const { authenticated, clientId, clientInfo } = await verifyAuth()
    if (!authenticated || !clientId || !clientInfo) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Parallel queries for better performance
    const [planResult, credentialsResult] = await Promise.all([
      // Get plan connection limit
      supabase
        .from('plans')
        .select('connections')
        .eq('name', clientInfo.plan_type)
        .single(),
      
      // Get client credentials
      supabase
        .from('client_credentials')
        .select('*')
        .eq('client_id', clientId)
        .maybeSingle()
    ])

    // Handle plan data
    const { data: planData, error: planError } = planResult
    let limit = 1 // Default fallback
    
    if (planError) {
      console.error('Error fetching plan data:', planError)
    } else {
      limit = planData?.connections || 1
    }

    // Handle credentials data
    const { data: credentialsData, error: credentialsError } = credentialsResult
    let sanitizedCredentials: SanitizedCredentials

    if (credentialsError) {
      console.error('Error fetching client credentials:', credentialsError)
      return NextResponse.json(
        { error: 'Failed to load connection data' },
        { status: 500 }
      )
    }

    // If no credentials record exists, create a new one
    if (!credentialsData) {
      const { data: newData, error: insertError } = await supabase
        .from('client_credentials')
        .insert({
          client_id: clientId,
          fb_url: '',
          ig_url: '',
          wa_url: '',
          tg_url: '',
          web_url: '',
          fb_token: '',
          ig_token: '',
          wa_token: ''
        })
        .select()
        .single()

      if (insertError) {
        console.error('Error creating client credentials record:', insertError)
        return NextResponse.json(
          { error: 'Failed to initialize connection data' },
          { status: 500 }
        )
      }

      // Return sanitized new data
      sanitizedCredentials = {
        id: newData.id,
        client_id: newData.client_id,
        fb_name: newData.fb_name,
        ig_name: newData.ig_name,
        tg_name: newData.tg_name,
        wa_name: newData.wa_name,
        web_name: newData.web_name,
        web_domain: newData.web_domain,
        fb_status: newData.fb_status,
        ig_status: newData.ig_status,
        wa_status: newData.wa_status,
        tg_status: newData.tg_status,
        web_status: newData.web_status,
        tg_biz_id: newData.tg_biz_id,
        has_fb_token: false,
        has_ig_token: false,
        has_wa_token: false,
        has_tg_token: false,
        has_tg_biz: false,
        has_web_domain: false
      }
    } else {
      // Return sanitized existing data
      sanitizedCredentials = {
        id: credentialsData.id,
        client_id: credentialsData.client_id,
        fb_name: credentialsData.fb_name,
        ig_name: credentialsData.ig_name,
        tg_name: credentialsData.tg_name,
        wa_name: credentialsData.wa_name,
        web_name: credentialsData.web_name,
        web_domain: credentialsData.web_domain,
        fb_status: credentialsData.fb_status,
        ig_status: credentialsData.ig_status,
        wa_status: credentialsData.wa_status,
        tg_status: credentialsData.tg_status,
        web_status: credentialsData.web_status,
        tg_biz_id: credentialsData.tg_biz_id,
        has_fb_token: !!credentialsData.fb_token,
        has_ig_token: !!credentialsData.ig_token,
        has_wa_token: !!credentialsData.wa_token,
        has_tg_token: !!credentialsData.tg_token,
        has_tg_biz: !!credentialsData.tg_biz_id,
        has_web_domain: !!credentialsData.web_domain
      }
    }

    const response: ConnectionDataResponse = {
      limit,
      plan_type: clientInfo.plan_type || '',
      credentials: sanitizedCredentials
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error in connection-data API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
