import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function POST(request: Request) {
  try {
    // Verify authentication
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const { platform } = await request.json()

    if (!platform) {
      return NextResponse.json(
        { error: 'Missing required field: platform' },
        { status: 400 }
      )
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Get current credentials to extract platform identifiers for webhook
    const { data: credentials, error: credentialsError } = await supabase
      .from('client_credentials')
      .select('fb_url, ig_url, tg_url, web_domain, tg_token, tg_biz_id')
      .eq('client_id', clientId)
      .maybeSingle()

    if (credentialsError) {
      console.error('Error fetching credentials:', credentialsError)
      return NextResponse.json(
        { error: 'Failed to fetch connection data' },
        { status: 500 }
      )
    }

    if (!credentials) {
      return NextResponse.json(
        { error: 'No connection data found' },
        { status: 404 }
      )
    }

    // Determine which fields to update based on platform
    let updateFields = {}
    let platformIdentifier = ''
    let platformPrefix = ''

    switch (platform) {
      case 'facebook':
        updateFields = { fb_token: '', fb_status: 0, fb_id: '' }
        platformPrefix = 'fb'
        const fbUrl = credentials.fb_url || ''
        if (fbUrl) {
          const urlParts = fbUrl.split('/')
          platformIdentifier = urlParts[urlParts.length - 1]
        }
        break
      case 'instagram':
        updateFields = { ig_token: '', ig_status: 0, ig_id: '' }
        platformPrefix = 'ig'
        const igUrl = credentials.ig_url || ''
        if (igUrl) {
          const urlParts = igUrl.split('/')
          platformIdentifier = urlParts[urlParts.length - 1]
        }
        break
      case 'telegram':
        // Check if it's a Telegram Business connection
        if (credentials.tg_biz_id) {
          updateFields = { tg_biz_id: '', tg_status: 0, tg_id: '' }
          platformIdentifier = credentials.tg_biz_id
        } else {
          updateFields = { tg_token: '', tg_status: 0, tg_id: '' }
          // For Telegram Bot, extract from URL
          const tgUrl = credentials.tg_url || ''
          if (tgUrl) {
            const urlParts = tgUrl.split('/')
            platformIdentifier = urlParts[urlParts.length - 1]
          }
        }
        platformPrefix = 'tg'
        break
      case 'web':
        updateFields = { web_domain: '', web_status: 0 }
        platformPrefix = 'web'
        platformIdentifier = credentials.web_domain || ''
        break
      default:
        return NextResponse.json(
          { error: `Unknown platform: ${platform}` },
          { status: 400 }
        )
    }

    // Special handling for Telegram - delete webhook before removing token
    if (platform === 'telegram' && credentials.tg_token) {
      try {
        const token = credentials.tg_token
        // Call Telegram API to delete the webhook
        const telegramResponse = await fetch(`https://api.telegram.org/bot${token}/deleteWebhook`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
          // No body needed for deleteWebhook
        })

        const telegramData = await telegramResponse.json()

        if (!telegramData.ok) {
          console.warn('Warning: Failed to delete Telegram webhook:', telegramData.description)
          // Continue with deletion even if webhook deletion fails
        } else {
          console.log('Successfully deleted Telegram webhook')
        }
      } catch (telegramError) {
        console.warn('Error deleting Telegram webhook:', telegramError)
        // Continue with deletion even if webhook deletion fails
      }
    }

    // Update the database to clear the platform data
    const { error: updateError } = await supabase
      .from('client_credentials')
      .update(updateFields)
      .eq('client_id', clientId)

    if (updateError) {
      console.error(`Error disconnecting ${platform}:`, updateError)
      return NextResponse.json(
        { error: `Failed to disconnect ${platform}: ${updateError.message}` },
        { status: 500 }
      )
    }

    // Send platform disconnection data via external webhook (if we have identifier)
    if (platformIdentifier && platformPrefix) {
      try {
        const webhookUrl = process.env.DISCONNECT_PLATFORM_WEBHOOK_URL

        if (webhookUrl) {
          const webhookPayload = {
            platformPrefix: platformPrefix,
            platformIdentifier: platformIdentifier,
            action: 'Disconnect-Platform',
            source: 'chhlatbot-dashboard',
            version: '1.0'
          }

          const webhookResponse = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(webhookPayload),
          })

          if (!webhookResponse.ok) {
            console.warn(`Warning: Failed to send disconnect data for ${platform}:`, webhookResponse.status)
            // Continue execution even if webhook fails
          } else {
            console.log(`Successfully sent disconnect data for ${platform}`)
          }
        } else {
          console.warn('DISCONNECT_PLATFORM_WEBHOOK_URL not configured')
        }
      } catch (webhookError) {
        console.warn(`Warning: Error sending disconnect data for ${platform}:`, webhookError)
        // Continue execution even if webhook fails
      }
    } else {
      console.warn(`Warning: No platformIdentifier or platformPrefix found for ${platform}, skipping external webhook`)
    }

    return NextResponse.json({
      success: true,
      message: `Successfully disconnected ${platform}`,
      platform
    })

  } catch (error) {
    console.error('Error in disconnect API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
