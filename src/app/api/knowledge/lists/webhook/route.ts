import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'

export async function POST(request: Request) {
  try {
    // Verify authentication
    const { authenticated } = await verifyAuth()
    if (!authenticated) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const { 
      faq_id, 
      client_id, 
      answer, 
      sector, 
      lang, 
      audio_url, 
      is_audio
    } = await request.json()

    // Get the webhook URL from environment
    const webhookUrl = process.env.FAQ_UPDATE_EN_WEBHOOK_URL
    if (!webhookUrl) {
      console.warn('FAQ webhook URL not configured')
      return NextResponse.json({ success: false, error: 'Webhook URL not configured' })
    }

    // Prepare webhook payload
    const webhookData = {
      faq_id,
      client_id,
      answer,
      sector,
      lang: lang || 'en',
      audio_url: audio_url || '',
      is_audio: !!is_audio
    }

    // Send webhook (fire and forget)
    fetch(webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(webhookData)
    }).catch(console.error)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in webhook endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 