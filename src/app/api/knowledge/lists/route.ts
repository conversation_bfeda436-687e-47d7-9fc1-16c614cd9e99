import { NextRequest, NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

// Type definitions
type KnowledgeItem = {
  id: number
  question_p: string
  answer_p: string
  created_at: string
  updated_at: string
  audio_url?: string
  audio_duration?: number
  photo_url?: any
  photo_id?: string
  audio_file_path?: string
  client_id: string
}

// GET - Fetch all knowledge items for the client
export async function GET() {
  try {
    // Authentication check
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Fetch knowledge items with essential fields only
    const { data, error } = await supabase
      .from('faqs')
      .select('id, faq_id, question_p, answer_p, created_at, updated_at, audio_url, audio_duration, photo_url, photo_id, audio_file_path, client_id')
      .eq('client_id', clientId)
      .eq('is_visible', true)
      .order('updated_at', { ascending: false })

    if (error) {
      console.error('Error fetching knowledge items:', error)
      return NextResponse.json(
        { error: 'Failed to fetch knowledge items' },
        { status: 500 }
      )
    }

    // Process data for frontend
    const processedData = data?.map(item => ({
      ...item,
      question: item.question_p || '',
      answer: item.answer_p || ''
    })) || []

    return NextResponse.json({ items: processedData })

  } catch (error) {
    console.error('Error in knowledge lists GET API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Update an existing knowledge item
export async function POST(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const { faq_id, updateData } = await request.json()

    if (!faq_id || !updateData) {
      return NextResponse.json(
        { error: 'Missing required fields: faq_id and updateData' },
        { status: 400 }
      )
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Update the knowledge item (RLS + performance: use indexed client_id first)
    const { data, error: updateError } = await supabase
      .from('faqs')
      .update(updateData)
      .eq('client_id', clientId)  // Client ID condition first
      .eq('faq_id', faq_id)       // Then FAQ ID condition
      .select()

    if (updateError) {
      console.error('Error updating knowledge item:', updateError)
      return NextResponse.json(
        { error: 'Failed to update knowledge item' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      success: true,
      data
    })

  } catch (error) {
    console.error('Error in knowledge lists POST API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE - Delete a knowledge item
export async function DELETE(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get faq_id from URL search params
    const { searchParams } = new URL(request.url)
    const faq_id = searchParams.get('faq_id')

    if (!faq_id) {
      return NextResponse.json(
        { error: 'Missing required parameter: faq_id' },
        { status: 400 }
      )
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Delete the knowledge item (RLS + performance: use indexed client_id first)
    const { error: deleteError } = await supabase
      .from('faqs')
      .delete()
      .eq('client_id', clientId)
      .eq('faq_id', faq_id)

    if (deleteError) {
      console.error('Error deleting knowledge item:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete knowledge item' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error in knowledge lists DELETE API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
