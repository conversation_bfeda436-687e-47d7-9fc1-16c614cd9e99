import { NextRequest, NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

// GET - Fetch welcome chat data for the client
export async function GET() {
  try {
    // Authentication check
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Fetch welcome chat data with RLS + performance (indexed client_id first)
    const { data, error } = await supabase
      .from('welcome_chat')
      .select('*')
      .eq('client_id', clientId)

    if (error) {
      console.error('Error fetching welcome chat data:', error)
      return NextResponse.json(
        { error: 'Failed to fetch welcome chat data' },
        { status: 500 }
      )
    }

    return NextResponse.json({ welcomeData: data || [] })

  } catch (error) {
    console.error('Error in welcome chat GET API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT - Update welcome chat data
export async function PUT(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { chat_id, updateData } = body

    if (!chat_id || !updateData) {
      return NextResponse.json(
        { error: 'Missing required fields: chat_id, updateData' },
        { status: 400 }
      )
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Update welcome chat with RLS + performance (indexed client_id first)
    const { error: updateError } = await supabase
      .from('welcome_chat')
      .update(updateData)
      .eq('client_id', clientId)
      .eq('chat_id', chat_id)

    if (updateError) {
      console.error('Error updating welcome chat:', updateError)
      return NextResponse.json(
        { error: 'Failed to update welcome chat' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error in welcome chat PUT API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
