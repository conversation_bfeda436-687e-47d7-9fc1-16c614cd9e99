import { NextRequest, NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

// GET - Fetch all photos for the client (reuse existing list-photos logic)
export async function GET() {
  try {
    // Authentication check
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Fetch photos with RLS + performance (indexed client_id first)
    const { data, error } = await supabase
      .from('photos')
      .select('id, photo_id, photo_url, photo_file_path, updated_at')
      .eq('client_id', clientId)
      .order('updated_at', { ascending: false })

    if (error) {
      console.error('Error fetching photos:', error)
      return NextResponse.json(
        { error: 'Failed to fetch photos' },
        { status: 500 }
      )
    }

    return NextResponse.json({ photos: data || [] })

  } catch (error) {
    console.error('Error in photos GET API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Create new photo
export async function POST(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { photo_id, photo_url, photo_file_path } = body

    if (!photo_id || !photo_url || !photo_file_path) {
      return NextResponse.json(
        { error: 'Missing required fields: photo_id, photo_url, photo_file_path' },
        { status: 400 }
      )
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Insert photo with explicit client_id (RLS + performance)
    const { error: insertError } = await supabase
      .from('photos')
      .insert({
        photo_id,
        client_id: clientId,
        photo_url,
        photo_file_path
      })

    if (insertError) {
      console.error('Error inserting photo:', insertError)
      return NextResponse.json(
        { error: 'Failed to create photo' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error in photos POST API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT - Update existing photo
export async function PUT(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { photo_id, photo_url, photo_file_path } = body

    if (!photo_id || !photo_url || !photo_file_path) {
      return NextResponse.json(
        { error: 'Missing required fields: photo_id, photo_url, photo_file_path' },
        { status: 400 }
      )
    }

    // Ensure photo_id is uppercase
    const capitalizedPhotoId = photo_id.toUpperCase()

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Update photo with RLS + performance (indexed client_id first)
    const { error: updateError } = await supabase
      .from('photos')
      .update({
        photo_url,
        photo_file_path
      })
      .eq('client_id', clientId)
      .eq('photo_id', capitalizedPhotoId)

    if (updateError) {
      console.error('Error updating photo:', updateError)
      return NextResponse.json(
        { error: 'Failed to update photo' },
        { status: 500 }
      )
    }

    // Also update any FAQs that use this photo_id
    try {
      await supabase
        .from('faqs')
        .update({
          photo_url: photo_url,
          fb_photo_atmid: null,
          tg_photo_atmid: null,
          ig_photo_atmid: null
        })
        .eq('client_id', clientId)
        .eq('photo_id', capitalizedPhotoId)
    } catch (faqUpdateError) {
      // Log but don't fail - it's possible no FAQs use this photo_id
      console.warn('Error updating FAQs with new photo URLs:', faqUpdateError)
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error in photos PUT API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE - Delete photo (with FAQ dependency check)
export async function DELETE(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get photo_id from URL search params
    const { searchParams } = new URL(request.url)
    const photo_id = searchParams.get('photo_id')

    if (!photo_id) {
      return NextResponse.json(
        { error: 'Missing required parameter: photo_id' },
        { status: 400 }
      )
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Check if photo is linked to any FAQs (RLS + performance)
    const { data: linkedFaqs, error: faqsError } = await supabase
      .from('faqs')
      .select('id, question, question_p')
      .eq('client_id', clientId)
      .eq('photo_id', photo_id)

    if (faqsError) {
      console.error('Error checking linked FAQs:', faqsError)
      return NextResponse.json(
        { error: 'Failed to check photo dependencies' },
        { status: 500 }
      )
    }

    // If there are linked FAQs, return dependency error
    if (linkedFaqs && linkedFaqs.length > 0) {
      const questions = linkedFaqs.map(faq => {
        const questionText = faq.question_p || faq.question
        return questionText.length > 30 ? `${questionText.substring(0, 30)}...` : questionText
      })

      return NextResponse.json(
        { 
          error: 'Photo is linked to FAQs',
          linkedQuestions: questions
        },
        { status: 409 } // Conflict status
      )
    }

    // Delete photo record (RLS + performance)
    const { error: deleteError } = await supabase
      .from('photos')
      .delete()
      .eq('client_id', clientId)
      .eq('photo_id', photo_id)

    if (deleteError) {
      console.error('Error deleting photo:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete photo' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error in photos DELETE API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
