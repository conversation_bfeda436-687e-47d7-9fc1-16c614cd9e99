import { NextRequest, NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

// GET - Check if photo is linked to any FAQs
export async function GET(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get photo_id from URL search params
    const { searchParams } = new URL(request.url)
    const photo_id = searchParams.get('photo_id')
    const lang = searchParams.get('lang') || 'en'

    if (!photo_id) {
      return NextResponse.json(
        { error: 'Missing required parameter: photo_id' },
        { status: 400 }
      )
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Check if photo is linked to any FAQs (RLS + performance: indexed client_id first)
    const { data: linkedFaqs, error: faqsError } = await supabase
      .from('faqs')
      .select('id, question, question_p')
      .eq('client_id', clientId)
      .eq('photo_id', photo_id)

    if (faqsError) {
      console.error('Error checking linked FAQs:', faqsError)
      return NextResponse.json(
        { error: 'Failed to check photo dependencies' },
        { status: 500 }
      )
    }

    // Process linked questions based on language
    const questions = (linkedFaqs || []).map(faq => {
      const questionText = lang === 'kh' && faq.question_p ? faq.question_p : faq.question
      // Truncate to first 30 characters
      return questionText.length > 30 ? `${questionText.substring(0, 30)}...` : questionText
    })

    return NextResponse.json({
      hasLinkedFaqs: linkedFaqs && linkedFaqs.length > 0,
      linkedQuestions: questions,
      count: linkedFaqs ? linkedFaqs.length : 0
    })

  } catch (error) {
    console.error('Error in photo dependency check API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
