import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { nanoid } from 'nanoid'

export async function POST(request: Request) {
  try {
    // Verify authentication and get client info
    const { authenticated, clientId, clientInfo } = await verifyAuth()
    if (!authenticated || !clientId || !clientInfo) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const { knowledgeItems } = await request.json()

    if (!knowledgeItems || !Array.isArray(knowledgeItems) || knowledgeItems.length === 0) {
      return NextResponse.json(
        { error: 'Missing required field: knowledgeItems' },
        { status: 400 }
      )
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Get authenticated user for file uploads (needed for storage path)
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user?.id) {
      return NextResponse.json(
        { error: 'Could not verify user authentication' },
        { status: 401 }
      )
    }

    const bucketName = 'audios'
    let itemsProcessed = 0
    const results = []

    // Process each knowledge item
    for (const item of knowledgeItems) {
      try {
        let targetFilePath: string | null = null
        let publicAudioUrl: string | null = null
        let audioDurationValue: number | null = item.audioDuration || null

        // Handle audio upload if present
        if (item.audioBlob && item.audioBlob.length > 0) {
          // Convert base64 to blob
          const audioBuffer = Buffer.from(item.audioBlob, 'base64')
          
          // Determine file extension
          const mimeType = item.audioMimeType || 'audio/webm'
          let fileExtension = 'webm'
          if (mimeType.includes('mp4')) fileExtension = 'mp4'
          else if (mimeType.includes('opus')) fileExtension = 'opus'
          else if (mimeType.includes('ogg')) fileExtension = 'oga'

          // Generate unique file path
          const timestamp = Date.now()
          const randomId = nanoid(9)
          targetFilePath = `${user.id}/${timestamp}_${randomId}.${fileExtension}`

          // Upload to Supabase Storage
          const { data: uploadData, error: uploadError } = await supabase.storage
            .from(bucketName)
            .upload(targetFilePath, audioBuffer, {
              contentType: mimeType,
              cacheControl: '3600',
              upsert: false
            })

          if (uploadError) {
            console.error('Audio upload error:', uploadError)
            throw new Error(`Failed to upload audio: ${uploadError.message}`)
          }

          // Get public URL
          const { data: urlData } = supabase.storage
            .from(bucketName)
            .getPublicUrl(targetFilePath)

          publicAudioUrl = urlData.publicUrl
        }

        // Generate unique ID for the knowledge item
        const knowledgeId = nanoid(16)

        // Prepare knowledge item for database
        const knowledgeData = {
          faq_id: knowledgeId,
          created_at: new Date(),
          client_id: clientId,
          question_p: item.question,
          answer_p: publicAudioUrl ? "" : item.answer,
          audio_url: publicAudioUrl,
          audio_file_path: targetFilePath,
          audio_duration: publicAudioUrl ? Math.round(audioDurationValue ?? 0) : null,
          photo_url: item.photoInfo?.full_photo_urls || (item.photoInfo?.photo_url ? [item.photoInfo.photo_url] : null),
          photo_id: item.photoInfo?.photo_id || null
        }

        // Insert into database (RLS policies ensure user can only insert to their own client)
        const { error: insertError } = await supabase
          .from('faqs')
          .insert(knowledgeData)

        if (insertError) {
          console.error('Error inserting knowledge item:', insertError)
          throw new Error(`Failed to save knowledge item: ${insertError.message}`)
        }

        // Send webhook notification (fire and forget)
        const webhookUrl = process.env.FAQ_EN_WEBHOOK_URL;
        if (webhookUrl) {
          fetch(webhookUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              faq_id: knowledgeId,
              client_id: clientId,
              question: item.question,
              answer: item.answer,
              sector: clientInfo.sector,
              lang: clientInfo.lang || 'en',
              audioUrl: publicAudioUrl
            })
          }).catch(error => {
            console.error('Error sending FAQ webhook:', error);
          });
        }

        itemsProcessed++
        results.push({
          id: knowledgeId,
          question: item.question,
          success: true
        })

      } catch (itemError: any) {
        console.error(`Error processing knowledge item:`, itemError)
        results.push({
          question: item.question,
          success: false,
          error: itemError.message
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully processed ${itemsProcessed} knowledge items`,
      itemsProcessed,
      totalItems: knowledgeItems.length,
      results
    })

  } catch (error: any) {
    console.error('Error in knowledge add API:', error)
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    )
  }
}
