import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function GET(request: Request) {
  try {
    // Verify authentication
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100) // Cap at 100

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Fetch photos from database
    const { data, error } = await supabase
      .from('photos')
      .select('id, photo_id, photo_url, photo_file_path')
      .eq('client_id', clientId)
      .order('updated_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching photos:', error)
      return NextResponse.json(
        { error: 'Failed to fetch photos' },
        { status: 500 }
      )
    }

    // Transform data to hide internal structure
    const photos = (data || []).map(photo => ({
      id: photo.id,
      photoId: photo.photo_id,
      thumbnailUrl: photo.photo_url?.[0] || null,
      allUrls: photo.photo_url || [],
      filePaths: photo.photo_file_path || []
    }))

    return NextResponse.json({
      success: true,
      photos,
      count: photos.length,
      limit
    })

  } catch (error: any) {
    console.error('Error in list photos API:', error)
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    )
  }
}
