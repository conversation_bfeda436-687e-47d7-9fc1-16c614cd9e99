import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

// Define the minimal credentials type for disconnect page
type DisconnectCredentials = {
  id: number
  client_id: string
  fb_name?: string
  ig_name?: string
  tg_name?: string
  web_domain?: string
  tg_biz_id?: string
  // Connection status flags
  has_fb_token: boolean
  has_ig_token: boolean
  has_tg_token: boolean
  has_tg_biz: boolean
  has_web_domain: boolean
}

export async function GET() {
  try {
    // Single authentication check
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Query only the fields needed for disconnect page
    const { data: credentialsData, error: credentialsError } = await supabase
      .from('client_credentials')
      .select('id, client_id, fb_name, ig_name, tg_name, web_domain, tg_biz_id, fb_token, ig_token, tg_token')
      .eq('client_id', clientId)
      .maybeSingle()

    if (credentialsError) {
      console.error('Error fetching client credentials:', credentialsError)
      return NextResponse.json(
        { error: 'Failed to load connection data' },
        { status: 500 }
      )
    }

    // If no credentials record exists, return empty data
    if (!credentialsData) {
      const emptyCredentials: DisconnectCredentials = {
        id: 0,
        client_id: clientId,
        fb_name: undefined,
        ig_name: undefined,
        tg_name: undefined,
        web_domain: undefined,
        tg_biz_id: undefined,
        has_fb_token: false,
        has_ig_token: false,
        has_tg_token: false,
        has_tg_biz: false,
        has_web_domain: false
      }

      return NextResponse.json({ credentials: emptyCredentials })
    }

    // Return sanitized data with only necessary fields
    const sanitizedCredentials: DisconnectCredentials = {
      id: credentialsData.id,
      client_id: credentialsData.client_id,
      fb_name: credentialsData.fb_name,
      ig_name: credentialsData.ig_name,
      tg_name: credentialsData.tg_name,
      web_domain: credentialsData.web_domain,
      tg_biz_id: credentialsData.tg_biz_id,
      has_fb_token: !!credentialsData.fb_token,
      has_ig_token: !!credentialsData.ig_token,
      has_tg_token: !!credentialsData.tg_token,
      has_tg_biz: !!credentialsData.tg_biz_id,
      has_web_domain: !!credentialsData.web_domain
    }

    return NextResponse.json({ credentials: sanitizedCredentials })

  } catch (error) {
    console.error('Error in disconnect-data API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
