import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function POST(request: Request) {
  try {
    const { webhookId } = await request.json()

    // Verify authentication
    const { authenticated, clientId } = await verifyAuth()
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Get client credentials to retrieve webhook URLs
    const { data: credentials, error: credentialsError } = await supabase
      .from('client_credentials')
      .select('fb_url, ig_url, wa_url, tg_url, web_url')
      .eq('client_id', clientId)
      .maybeSingle()

    if (credentialsError) {
      console.error('Error fetching credentials:', credentialsError)
      return NextResponse.json(
        { error: 'Failed to fetch webhook URL' },
        { status: 500 }
      )
    }

    // Return the appropriate webhook URL
    let webhookUrl = '';

    if (webhookId === 'privacy-policy') {
      webhookUrl = 'https://www.chhlatbot.com/privacy';
    } else if (webhookId === 'fb-webhook') {
      webhookUrl = credentials?.fb_url || 'https://api.chhlatbot.com/webhook/messenger';
    } else if (webhookId === 'ig-webhook') {
      webhookUrl = credentials?.ig_url || 'https://api.chhlatbot.com/webhook/instagram';
    } else if (webhookId === 'wa-webhook') {
      webhookUrl = credentials?.wa_url || 'https://api.chhlatbot.com/webhook/whatsapp';
    } else if (webhookId === 'web-webhook') {
      webhookUrl = credentials?.web_url || 'https://api.chhlatbot.com/webhook/web';
    } else {
      return NextResponse.json(
        { error: 'Invalid webhook ID' },
        { status: 400 }
      )
    }

    return NextResponse.json({
      url: webhookUrl
    })

  } catch (error) {
    console.error('Error in webhook-url API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
